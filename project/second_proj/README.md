# EduAI Global - AI-Powered Education Platform

## 🌍 Mission
Democratize AI education globally through an accessible online platform that serves 50 million learners by 2030.

## 🏗 Architecture Overview

This is a production-ready monorepo containing:
- **Backend**: FastAPI + PostgreSQL + Redis
- **Frontend**: React + TypeScript + Vite
- **Shared**: Common types and utilities
- **Infrastructure**: Docker, Kubernetes, CI/CD

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Poetry 1.7+

### Setup Instructions

1. **Clone the repository**
```bash
git clone <repository-url>
cd eduai-global
```

2. **Install dependencies**
```bash
# Install root dependencies
npm install

# Install backend dependencies
cd backend
poetry install

# Install frontend dependencies
cd ../frontend
npm install
```

3. **Environment Configuration**
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Update the .env files with your configuration
```

4. **Database Setup**
```bash
# Create database
createdb eduai_global

# Run migrations
cd backend
poetry run alembic upgrade head
```

5. **Start Development Servers**
```bash
# From root directory
npm run dev
```

This will start:
- Backend API at http://localhost:8000
- Frontend at http://localhost:3000
- API Documentation at http://localhost:8000/api/v1/docs

## 📁 Project Structure

```
eduai-global/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── core/           # Core configurations
│   │   ├── models/         # SQLAlchemy models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # Business logic
│   │   ├── routes/         # API endpoints
│   │   └── utils/          # Utilities
│   ├── tests/              # Backend tests
│   ├── migrations/         # Alembic migrations
│   └── pyproject.toml      # Poetry configuration
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   ├── store/          # State management
│   │   └── types/          # TypeScript types
│   ├── tests/              # Frontend tests
│   └── package.json
├── shared/                 # Shared code
├── docs/                   # Documentation
├── infrastructure/         # Deployment configs
└── package.json           # Root package.json
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
poetry run pytest                    # Run all tests
poetry run pytest --cov              # With coverage
poetry run pytest tests/unit         # Unit tests only
poetry run pytest -k "test_auth"     # Specific tests
```

### Frontend Tests
```bash
cd frontend
npm test                             # Run all tests
npm run test:unit                    # Unit tests
npm run test:e2e                     # E2E tests
npm run test:coverage                # With coverage
```

## 🔧 Development Commands

### Backend
```bash
cd backend
poetry run uvicorn app.main:app --reload    # Start dev server
poetry run ruff check .                      # Lint code
poetry run black .                           # Format code
poetry run mypy .                            # Type check
poetry run alembic revision -m "message"     # Create migration
```

### Frontend
```bash
cd frontend
npm run dev                          # Start dev server
npm run build                        # Build for production
npm run lint                         # Lint code
npm run format                       # Format code
npm run type-check                   # Type check
```

### Root Commands
```bash
npm run dev                          # Start all services
npm run test                         # Run all tests
npm run lint                         # Lint all code
npm run format                       # Format all code
npm run docker:up                    # Start with Docker
```

## 🐳 Docker

### Build and Run
```bash
docker-compose up --build            # Build and start all services
docker-compose up backend            # Start backend only
docker-compose logs -f backend       # View backend logs
docker-compose down                  # Stop all services
```

### Production Build
```bash
docker build -f backend/Dockerfile -t eduai-backend backend/
docker build -f frontend/Dockerfile -t eduai-frontend frontend/
```

## 📊 Database

### Migrations
```bash
cd backend
poetry run alembic revision --autogenerate -m "Add new table"
poetry run alembic upgrade head
poetry run alembic downgrade -1
poetry run alembic history
```

### Database Schema
Key tables:
- `users` - Platform users
- `courses` - Course catalog
- `modules` - Course content modules
- `user_progress` - Learning progress tracking
- `enrollments` - Course enrollments
- `subscriptions` - Payment subscriptions

## 🔐 API Authentication

The API uses JWT tokens for authentication:

1. **Register**: POST `/api/v1/auth/register`
2. **Login**: POST `/api/v1/auth/login`
3. **Refresh**: POST `/api/v1/auth/refresh`
4. **Include token**: `Authorization: Bearer <token>`

## 🌐 Environment Variables

### Backend (.env)
```env
# Database
DATABASE_URL=postgresql://user:pass@localhost/eduai_global
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changeme

# External APIs
OPENAI_API_KEY=your-openai-key
STRIPE_SECRET_KEY=your-stripe-key
SENDGRID_API_KEY=your-sendgrid-key
```

### Frontend (.env)
```env
VITE_API_URL=http://localhost:8000
VITE_STRIPE_PUBLIC_KEY=your-stripe-public-key
```

## 📝 API Documentation

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc
- OpenAPI JSON: http://localhost:8000/api/v1/openapi.json

## 🚢 Deployment

### Production Checklist
- [ ] Update environment variables
- [ ] Enable HTTPS
- [ ] Configure CORS origins
- [ ] Set up monitoring (Sentry)
- [ ] Configure backups
- [ ] Set up CI/CD pipelines
- [ ] Load testing
- [ ] Security audit

### Deployment Options
1. **Docker Compose** - Simple deployment
2. **Kubernetes** - Scalable deployment
3. **Cloud Platforms** - AWS, GCP, Azure
4. **PaaS** - Heroku, Railway, Render

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Style
- Backend: Black, Ruff, MyPy
- Frontend: ESLint, Prettier
- Commits: Conventional Commits

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Documentation: `/docs`
- Issues: GitHub Issues
- Email: <EMAIL>

## 🔗 Links

- [API Documentation](http://localhost:8000/api/v1/docs)
- [Project Wiki](./docs)
- [Architecture Diagram](./docs/architecture-blueprint.md)
