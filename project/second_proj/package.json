{"name": "eduai-global", "private": true, "workspaces": ["frontend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && poetry run uvicorn app.main:app --reload", "dev:frontend": "cd frontend && npm run dev", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && poetry run pytest", "test:frontend": "cd frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && poetry run ruff check . && poetry run mypy .", "lint:frontend": "cd frontend && npm run lint", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && poetry run black .", "format:frontend": "cd frontend && npm run prettier", "e2e": "cd frontend && npm run test:e2e", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "db:migrate": "cd backend && poetry run alembic upgrade head", "db:rollback": "cd backend && poetry run alembic downgrade -1", "db:reset": "cd backend && poetry run alembic downgrade base && poetry run alembic upgrade head"}, "devDependencies": {"concurrently": "^8.2.2"}}