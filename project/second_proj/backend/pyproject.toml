[tool.poetry]
name = "eduai-global-backend"
version = "1.0.0"
description = "Backend API for EduAI Global - AI Education Platform"
authors = ["EduAI Global Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.109.0"
uvicorn = {extras = ["standard"], version = "^0.27.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"
sqlalchemy = "^2.0.25"
alembic = "^1.13.1"
asyncpg = "^0.29.0"
psycopg2-binary = "^2.9.9"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
redis = "^5.0.1"
celery = "^5.3.6"
httpx = "^0.26.0"
email-validator = "^2.1.0"
python-dateutil = "^2.8.2"
loguru = "^0.7.2"
stripe = "^7.12.0"
sendgrid = "^6.11.0"
openai = "^1.10.0"
langchain = "^0.1.4"
pinecone-client = "^3.0.0"
pandas = "^2.1.4"
numpy = "^1.26.3"
scikit-learn = "^1.4.0"
boto3 = "^1.34.0"
Pillow = "^10.2.0"
python-magic = "^0.4.27"
aiofiles = "^23.2.1"
twilio = "^8.11.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.4"
pytest-asyncio = "^0.23.3"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
black = "^23.12.1"
ruff = "^0.1.13"
mypy = "^1.8.0"
types-redis = "^4.6.0"
types-python-dateutil = "^2.8.19"
types-python-jose = "^3.3.4"
types-passlib = "^1.7.7"
httpx = "^0.26.0"
factory-boy = "^3.3.0"
faker = "^22.2.0"
respx = "^0.20.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --cov=app --cov-report=html --cov-report=term"
testpaths = ["tests"]
asyncio_mode = "auto"
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.ruff]
line-length = 88
target-version = "py312"
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "C",   # flake8-comprehensions
    "B",   # flake8-bugbear
    "UP",  # pyupgrade
    "S",   # flake8-bandit
    "A",   # flake8-builtins
    "DTZ", # flake8-datetimez
    "ICN", # flake8-import-conventions
    "PIE", # flake8-pie
    "PT",  # flake8-pytest-style
    "RET", # flake8-return
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
]
ignore = [
    "E501",  # line too long
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert
    "A003",  # shadowing builtin
]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["S101", "ARG", "FBT"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "sendgrid.*",
    "pinecone.*",
    "langchain.*",
    "stripe.*",
    "twilio.*",
    "magic.*",
]
ignore_missing_imports = true

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__init__.py",
]

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
