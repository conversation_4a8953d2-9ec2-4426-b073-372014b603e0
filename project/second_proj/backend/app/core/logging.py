"""
Logging configuration using loguru
"""
import sys
from pathlib import Path
from loguru import logger

from app.core.config import settings


def setup_logging():
    """
    Configure loguru for the application
    """
    # Remove default logger
    logger.remove()
    
    # Console logging
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=settings.LOG_LEVEL,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # File logging for production
    if settings.ENVIRONMENT == "production":
        log_path = Path("logs")
        log_path.mkdir(exist_ok=True)
        
        # General log file
        logger.add(
            log_path / "app.log",
            rotation="500 MB",
            retention="10 days",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            backtrace=True,
            diagnose=True,
        )
        
        # Error log file
        logger.add(
            log_path / "error.log",
            rotation="500 MB",
            retention="30 days",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            backtrace=True,
            diagnose=True,
        )
    
    # Configure Sentry if DSN is provided
    if settings.SENTRY_DSN:
        import sentry_sdk
        from sentry_sdk.integrations.logging import LoggingIntegration
        from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
        from sentry_sdk.integrations.redis import RedisIntegration
        
        sentry_logging = LoggingIntegration(
            level=None,  # Capture all levels
            event_level=None  # Don't send logs as events
        )
        
        sentry_sdk.init(
            dsn=settings.SENTRY_DSN,
            environment=settings.ENVIRONMENT,
            integrations=[
                sentry_logging,
                SqlalchemyIntegration(),
                RedisIntegration(),
            ],
            traces_sample_rate=0.1,
            profiles_sample_rate=0.1,
        )
        
        logger.info("Sentry logging configured")
    
    logger.info(f"Logging configured for {settings.ENVIRONMENT} environment")
