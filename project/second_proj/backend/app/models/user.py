"""
User model for the database
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, String, Boolean, DateTime, Enum, JSON, 
    Index, UniqueConstraint, text
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class UserRole(str, enum.Enum):
    """User role enumeration"""
    STUDENT = "student"
    EDUCATOR = "educator"
    ADMIN = "admin"
    SUPERUSER = "superuser"


class SubscriptionTier(str, enum.Enum):
    """Subscription tier enumeration"""
    FREE = "free"
    PREMIUM = "premium"
    CORPORATE = "corporate"


class User(Base):
    """
    User model representing platform users
    """
    __tablename__ = "users"
    
    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        server_default=text("gen_random_uuid()"),
        nullable=False
    )
    
    # Authentication fields
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile fields
    name = Column(String(255), nullable=False)
    preferred_language = Column(String(5), default="en", nullable=False)
    timezone = Column(String(50), default="UTC", nullable=False)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(String(1000), nullable=True)
    
    # Role and permissions
    role = Column(
        Enum(UserRole),
        default=UserRole.STUDENT,
        nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Subscription
    subscription_tier = Column(
        Enum(SubscriptionTier),
        default=SubscriptionTier.FREE,
        nullable=False
    )
    subscription_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Profile data (JSONB for flexibility)
    profile_data = Column(JSON, default=dict, nullable=False)
    # Example structure:
    # {
    #     "education": {"level": "bachelor", "field": "Computer Science"},
    #     "experience": {"years": 5, "industry": "Tech"},
    #     "goals": ["career_switch", "skill_upgrade"],
    #     "interests": ["machine_learning", "data_science"],
    #     "location": {"country": "US", "city": "San Francisco"},
    #     "company": "Example Corp",
    #     "job_title": "Software Engineer"
    # }
    
    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=text("CURRENT_TIMESTAMP"),
        nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=text("CURRENT_TIMESTAMP"),
        onupdate=datetime.utcnow,
        nullable=False
    )
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    enrollments = relationship(
        "Enrollment",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    progress_records = relationship(
        "UserProgress",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    learning_profile = relationship(
        "UserLearningProfile",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )
    reviews = relationship(
        "CourseReview",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    study_group_memberships = relationship(
        "StudyGroupMember",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    mentorship_sessions_as_mentor = relationship(
        "MentorshipSession",
        foreign_keys="MentorshipSession.mentor_id",
        back_populates="mentor"
    )
    mentorship_sessions_as_mentee = relationship(
        "MentorshipSession",
        foreign_keys="MentorshipSession.mentee_id",
        back_populates="mentee"
    )
    certificates = relationship(
        "Certificate",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_user_email_active", "email", "is_active"),
        Index("idx_user_role", "role"),
        Index("idx_user_subscription", "subscription_tier"),
        Index("idx_user_created", "created_at"),
    )
    
    def __repr__(self):
        return f"<User {self.email}>"
    
    @property
    def is_premium(self) -> bool:
        """Check if user has premium access"""
        return self.subscription_tier in [
            SubscriptionTier.PREMIUM,
            SubscriptionTier.CORPORATE
        ]
    
    @property
    def display_name(self) -> str:
        """Get user's display name"""
        return self.name or self.email.split("@")[0]
    
    @property
    def is_educator_or_above(self) -> bool:
        """Check if user has educator privileges"""
        return self.role in [
            UserRole.EDUCATOR,
            UserRole.ADMIN,
            UserRole.SUPERUSER
        ]
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission"""
        permission_map = {
            UserRole.STUDENT: ["view_courses", "enroll", "view_progress"],
            UserRole.EDUCATOR: [
                "view_courses", "enroll", "view_progress",
                "create_content", "moderate_reviews"
            ],
            UserRole.ADMIN: [
                "view_courses", "enroll", "view_progress",
                "create_content", "moderate_reviews",
                "manage_users", "view_analytics"
            ],
            UserRole.SUPERUSER: ["*"]  # All permissions
        }
        
        user_permissions = permission_map.get(self.role, [])
        return "*" in user_permissions or permission in user_permissions
