<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EduAI Global - Democratizing AI Education for All</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/dist/reset.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/dist/reveal.css">

  <!-- Check for print-pdf mode and load the appropriate print styles -->
  <script>
    // Determine if the print-pdf query parameter is present
    var isPrintPDF = window.location.search.match(/print-pdf/gi);

    // Keep the black theme for both modes but add PDF styles when needed
    document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/dist/theme/black.css" id="theme">');

    // Add PDF-specific styles if in print mode
    if (isPrintPDF) {
      document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/css/print/pdf.css">');
    }
  </script>

  <style>
    /* Custom styles */
    .reveal {
      font-family: 'Open Sans', Helvetica, Arial, sans-serif;
      font-size: 30px;
      color: #f0f0f0;
      background-color: #1a1a2e;
    }

    html, body {
      background: linear-gradient(135deg, #1a1a2e 0%, #232344 100%);
    }

    .reveal h1,
    .reveal h2,
    .reveal h3,
    .reveal h4 {
      font-family: 'Montserrat', Helvetica, Arial, sans-serif;
      color: #00d09c;  /* Modern fintech green */
      text-transform: none;
      margin-bottom: 20px;
      letter-spacing: -0.01em;
    }

    .reveal h1 {
      font-size: 2.3em;
      font-weight: 700;
      line-height: 1.2;
    }

    .reveal h2 {
      font-weight: 600;
      line-height: 1.3;
    }

    .reveal h3 {
      font-size: 1.3em;
      font-weight: 600;
      color: #8ed8f8;
      line-height: 1.4;
    }

    .reveal ul {
      display: block;
      margin-left: 2em;
    }

    .reveal li {
      margin: 0.6em 0;
      line-height: 1.5;
      font-size: 0.92em;
      font-weight: 300;
    }

    .reveal a {
      color: #00d09c;
      text-decoration: none;
      transition: color 0.15s ease;
    }

    .reveal a:hover {
      color: #33ddb3;
      text-shadow: none;
      border: none;
    }

    .highlight-box {
      background-color: rgba(91, 194, 231, 0.1);
      border-left: 4px solid #00d09c;
      padding: 12px 20px;
      margin: 15px auto;
      border-radius: 0 4px 4px 0;
      font-size: 0.92em;
      font-weight: 300;
      line-height: 1.5;
      max-width: 1000px;
      width: 80%;
      text-align: center;
    }

    .logo-footer {
      position: fixed;
      bottom: 20px;
      left: 20px;
      z-index: 30;
      width: 150px; /* Adjust size as needed */
      height: auto;
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }
    .logo-footer:hover {
      opacity: 1;
    }

    /* Title slide specific styling */
    .title-slide {
      display: flex !important;
      flex-direction: column; /* Stack elements vertically */
      justify-content: center !important;
      align-items: center !important;
      height: 100vh !important;
      margin-top: 0 !important;
      text-align: center;
    }

    .title-logo {
      max-width: 250px; /* Increased size */
      margin-bottom: 30px;
    }

    /* Two column layout */
    .two-columns {
      display: flex;
      justify-content: space-between;
      gap: 30px;
      margin-top: 15px;
    }

    .column {
      flex: 1;
      padding: 0 10px;
    }

    /* Table styling */
    table {
      width: 80%;
      max-width: 1000px;
      margin: 15px auto;
      border-collapse: collapse;
      font-size: 0.75em;
      background-color: rgba(26, 26, 46, 0.6);
      border-radius: 8px;
      overflow: hidden;
    }

    th, td {
      padding: 12px 15px; /* Increased padding */
      text-align: left;
      border-bottom: 1px solid #363658;
    }

    th {
      background-color: rgba(91, 194, 231, 0.15);
      font-weight: 600;
      color: #8ed8f8;
    }

    tr:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    tr.highlight-row {
      background-color: rgba(91, 194, 231, 0.15);
    }

    tr.highlight-row td {
      border-bottom: 1px solid #5bc2e7;
    }

    /* Success story boxes */
    .success-story {
      background-color: rgba(91, 194, 231, 0.1);
      border-left: 4px solid #5bc2e7;
      border-radius: 0 4px 4px 0;
      padding: 12px 20px;
      margin: 15px auto;
      font-size: 0.85em;
      max-width: 1000px;
      width: 80%;
    }

    .success-story h3 {
      margin-top: 0;
      color: #5bc2e7;
    }

    /* Fragment animations */
    .fragment.highlight-current-blue {
      opacity: 1;
      visibility: inherit;
    }

    .fragment.highlight-current-blue.current-fragment {
      color: #5bc2e7;
      font-weight: bold;
    }

    /* Trial progress indicator */
    .trial-progress {
      display: flex;
      justify-content: space-between;
      width: 80%;
      max-width: 1000px;
      margin: 30px auto;
      position: relative;
    }

    .trial-progress:before {
      content: '';
      position: absolute;
      top: 20px;
      left: 0;
      width: 100%;
      height: 4px;
      background-color: rgba(255, 255, 255, 0.2);
      z-index: 1;
    }

    .trial-stage {
      position: relative;
      z-index: 2;
      text-align: center;
      width: 150px;
    }

    .stage-dot {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #232344;
      border: 3px solid rgba(255, 255, 255, 0.3);
      margin: 0 auto 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .stage-dot.completed {
      background-color: #5bc2e7;
      border-color: #8ed8f8;
    }

    .stage-dot.active {
      background-color: rgba(91, 194, 231, 0.3);
      border-color: #5bc2e7;
    }

    .stage-label {
      font-size: 0.8em;
      color: rgba(255, 255, 255, 0.7);
    }

    .stage-label.completed, .stage-label.active {
      color: #f0f0f0;
      font-weight: 600;
    }

    /* Benefits grid */
    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* More responsive */
      gap: 20px;
      max-width: 1000px;
      margin: 30px auto;
      width: 80%;
    }

    .benefit-card {
      background-color: rgba(26, 26, 46, 0.6);
      border-radius: 8px;
      padding: 20px;
      border: 1px solid rgba(91, 194, 231, 0.2);
      transition: all 0.3s ease;
    }

    .benefit-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      border-color: rgba(91, 194, 231, 0.5);
    }

    .benefit-card h3 {
      margin-top: 0;
      font-size: 1.1em;
      text-align: center;
    }

    .benefit-card p {
      font-size: 0.8em;
      line-height: 1.5;
    }

    /* Stats grid specific styling */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      max-width: 800px;
      margin: 30px auto;
      width: 80%;
    }

    .stat-card {
      background: linear-gradient(135deg, rgba(0, 208, 156, 0.1) 0%, rgba(0, 208, 156, 0.05) 100%);
      border: 2px solid #00d09c;
      border-radius: 12px;
      padding: 25px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 208, 156, 0.2);
      background: linear-gradient(135deg, rgba(0, 208, 156, 0.15) 0%, rgba(0, 208, 156, 0.08) 100%);
    }

    .stat-number {
      font-size: 2.5em;
      font-weight: 700;
      color: #00d09c;
      margin-bottom: 10px;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.9em;
      color: #f0f0f0;
      line-height: 1.4;
    }

    /* Market size visualization */
    .market-size-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
      margin: 30px auto;
      max-width: 800px;
    }

    .market-bar {
      position: relative;
      background: linear-gradient(90deg, #00d09c 0%, #33ddb3 100%);
      height: 60px;
      border-radius: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      color: #1a1a2e;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .market-bar:hover {
      transform: translateX(10px);
      box-shadow: 0 5px 20px rgba(0, 208, 156, 0.3);
    }

    .market-bar.tam { width: 100%; }
    .market-bar.sam { width: 75%; }
    .market-bar.som { width: 40%; }

    .market-label {
      font-size: 0.9em;
    }

    .market-value {
      font-size: 1.2em;
      font-weight: 700;
    }

    /* Revenue projection chart */
    .revenue-chart {
      display: flex;
      align-items: flex-end;
      justify-content: space-around;
      height: 300px;
      margin: 40px auto;
      padding: 20px;
      background: rgba(26, 26, 46, 0.4);
      border-radius: 12px;
      max-width: 800px;
      width: 80%;
    }

    .revenue-bar {
      position: relative;
      background: linear-gradient(180deg, #00d09c 0%, #008866 100%);
      border-radius: 8px 8px 0 0;
      min-width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      color: white;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .revenue-bar:hover {
      transform: scale(1.05);
      box-shadow: 0 -5px 20px rgba(0, 208, 156, 0.4);
    }

    .revenue-bar-1 { height: 20%; }
    .revenue-bar-2 { height: 50%; }
    .revenue-bar-3 { height: 100%; }

    .revenue-label {
      position: absolute;
      bottom: -30px;
      font-size: 0.9em;
      color: #f0f0f0;
    }

    .revenue-value {
      position: absolute;
      top: -30px;
      font-size: 1.1em;
      color: #00d09c;
    }

    /* Phase timeline */
    .phase-timeline {
      display: flex;
      justify-content: space-between;
      width: 80%;
      max-width: 900px;
      margin: 40px auto;
      position: relative;
    }

    .phase-timeline:before {
      content: '';
      position: absolute;
      top: 30px;
      left: 10%;
      right: 10%;
      height: 4px;
      background: linear-gradient(90deg, #00d09c 0%, #33ddb3 50%, #00d09c 100%);
      z-index: 1;
    }

    .phase {
      position: relative;
      z-index: 2;
      text-align: center;
      width: 180px;
    }

    .phase-dot {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #1a1a2e;
      border: 4px solid #00d09c;
      margin: 0 auto 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      font-size: 1.2em;
      color: #00d09c;
      transition: all 0.3s ease;
    }

    .phase:hover .phase-dot {
      background-color: #00d09c;
      color: #1a1a2e;
      transform: scale(1.1);
    }

    .phase-title {
      font-size: 1.1em;
      font-weight: 600;
      color: #00d09c;
      margin-bottom: 8px;
    }

    .phase-details {
      font-size: 0.8em;
      color: #f0f0f0;
      line-height: 1.4;
    }

    /* Impact metrics grid */
    .impact-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      max-width: 900px;
      margin: 30px auto;
      width: 80%;
    }

    .impact-card {
      background: linear-gradient(135deg, rgba(0, 208, 156, 0.1) 0%, rgba(0, 208, 156, 0.05) 100%);
      border: 1px solid rgba(0, 208, 156, 0.3);
      border-radius: 10px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .impact-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 208, 156, 0.2);
      border-color: #00d09c;
    }

    .impact-metric {
      font-size: 2em;
      font-weight: 700;
      color: #00d09c;
      margin-bottom: 10px;
    }

    .impact-label {
      font-size: 0.85em;
      color: #f0f0f0;
      line-height: 1.4;
    }

    /* PDF & Fullscreen button styles */
    .custom-action-btn {
      position: fixed;
      z-index: 50;
      bottom: 20px;
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: rgba(0, 208, 156, 0.85);
      color: #1a1a2e;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      box-shadow: 0 2px 10px rgba(0, 208, 156, 0.3);
      cursor: pointer;
      margin-left: 8px;
      transition: background 0.3s, transform 0.2s;
    }
    .custom-action-btn:hover {
      background: rgba(0, 208, 156, 1);
      transform: scale(1.07);
    }

    /* Icon styling */
    .icon-large {
      font-size: 3em;
      color: #00d09c;
      margin-bottom: 20px;
    }

    .reveal .icon-inline {
      margin-right: 10px;
      color: #00d09c;
    }
  </style>

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div class="reveal">
    <!-- Logo in bottom left corner for non-title slides -->
    <i class="fas fa-brain logo-footer" style="font-size: 3em; color: #00d09c;"></i>

    <div class="slides">
      <!-- Title Slide -->
      <section id="title-slide" class="title-slide">
        <i class="fas fa-brain icon-large" style="font-size: 5em;"></i>
        <h1>EduAI Global</h1>
        <h3 style="margin-bottom: 40px; color: #33ddb3;">Democratizing AI Education for All</h3>
        <p style="font-size: 0.9em; opacity: 0.8;">Making comprehensive AI education accessible to every person on Earth</p>
        <p style="font-size: 0.8em; opacity: 0.8; margin-top: 40px;">Investor Presentation | 2025</p>
      </section>

      <!-- The AI Skills Crisis -->
      <section>
        <h2><i class="fas fa-exclamation-triangle icon-inline"></i>The Global AI Skills Crisis</h2>
        <div class="highlight-box">
          <p><strong>The world faces an unprecedented AI literacy gap that threatens economic equality</strong></p>
        </div>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">12%</div>
            <div class="stat-label">Global workforce with basic AI literacy</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">375M</div>
            <div class="stat-label">Jobs requiring AI reskilling by 2030</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">$15.7T</div>
            <div class="stat-label">Potential economic impact of AI by 2030</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">85%</div>
            <div class="stat-label">Developing nations lack AI education programs</div>
          </div>
        </div>
      </section>

      <!-- Market Opportunity -->
      <section>
        <h2><i class="fas fa-chart-line icon-inline"></i>Market Opportunity</h2>
        <div class="highlight-box">
          <p><strong>Addressing a massive and rapidly growing education technology market</strong></p>
        </div>
        <div class="market-size-container">
          <div class="market-bar tam">
            <span class="market-label">Total Addressable Market</span>
            <span class="market-value">$340B</span>
          </div>
          <div class="market-bar sam">
            <span class="market-label">Serviceable Addressable Market</span>
            <span class="market-value">$89B</span>
          </div>
          <div class="market-bar som">
            <span class="market-label">Serviceable Obtainable Market</span>
            <span class="market-value">$12B</span>
          </div>
        </div>
        <p style="text-align: center; font-size: 0.9em; margin-top: 20px;">AI-focused education is the fastest growing segment in EdTech</p>
      </section>

      <!-- Our Solution -->
      <section>
        <h2><i class="fas fa-lightbulb icon-inline"></i>Our Solution: EduAI Global</h2>
        <div class="highlight-box">
          <p><strong>"The Netflix of AI Education" - Bite-sized, engaging, accessible learning for everyone</strong></p>
        </div>
        <div class="two-columns" style="margin-top: 30px;">
          <div class="column">
            <h3><i class="fas fa-graduation-cap icon-inline"></i>Core Platform</h3>
            <ul>
              <li>Adaptive AI-powered learning paths</li>
              <li>5-15 minute microlearning modules</li>
              <li>Gamified achievement system</li>
              <li>Offline-first design</li>
              <li>25+ language support</li>
            </ul>
          </div>
          <div class="column">
            <h3><i class="fas fa-users icon-inline"></i>Community Features</h3>
            <ul>
              <li>Global peer learning network</li>
              <li>1:10 mentor-to-student ratio</li>
              <li>Real-world project portfolio</li>
              <li>Industry certifications</li>
              <li>500+ corporate partnerships</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Target Audiences -->
      <section>
        <h2><i class="fas fa-user-friends icon-inline"></i>Target Audiences</h2>
        <div class="benefits-grid">
          <div class="benefit-card">
            <h3><i class="fas fa-briefcase"></i> Working Professionals</h3>
            <p><strong>35-50 years</strong><br>Career transition and upskilling to stay relevant in AI-driven economy</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-university"></i> University Students</h3>
            <p><strong>18-25 years</strong><br>Career preparation and specialization in AI technologies</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-rocket"></i> Entrepreneurs</h3>
            <p><strong>25-45 years</strong><br>Business AI integration and innovation strategies</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-chalkboard-teacher"></i> Educators</h3>
            <p><strong>28-55 years</strong><br>Teaching AI concepts and curriculum development</p>
          </div>
        </div>
      </section>

      <!-- Competitive Advantage -->
      <section>
        <h2><i class="fas fa-trophy icon-inline"></i>Competitive Advantage</h2>
        <div class="highlight-box">
          <p><strong>Unique differentiators that set us apart in the global EdTech landscape</strong></p>
        </div>
        <div class="benefits-grid" style="margin-top: 30px;">
          <div class="benefit-card">
            <h3><i class="fas fa-globe-asia"></i> Cultural Relevance</h3>
            <p>AI applications tailored to local industries, challenges, and cultural contexts</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-coins"></i> Economic Accessibility</h3>
            <p>Sliding scale pricing based on purchasing power parity - truly global access</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-hands-helping"></i> Mentorship at Scale</h3>
            <p>Industry experts providing personalized guidance with AI-assisted matching</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-building"></i> Real-World Application</h3>
            <p>Practical projects with actual companies solving real business problems</p>
          </div>
        </div>
      </section>

      <!-- Technology Infrastructure -->
      <section>
        <h2><i class="fas fa-server icon-inline"></i>Technology Infrastructure</h2>
        <div class="two-columns">
          <div class="column">
            <h3><i class="fas fa-code icon-inline"></i>Platform Architecture</h3>
            <ul>
              <li>Cloud-first with edge computing</li>
              <li>AI recommendation engine</li>
              <li>Advanced learning analytics</li>
              <li>Mobile-first PWA design</li>
              <li>Blockchain-verified certificates</li>
            </ul>
          </div>
          <div class="column">
            <h3><i class="fas fa-play-circle icon-inline"></i>Learning Experience</h3>
            <ul>
              <li>Interactive AI simulations</li>
              <li>Virtual AI labs & playgrounds</li>
              <li>AR/VR immersive modules</li>
              <li>Live coding environments</li>
              <li>Collaborative workspaces</li>
            </ul>
          </div>
        </div>
        <div class="highlight-box" style="margin-top: 30px;">
          <p><strong>Built for scale: Supporting millions of concurrent learners globally</strong></p>
        </div>
      </section>

      <!-- Market Entry Strategy -->
      <section>
        <h2><i class="fas fa-map-marked-alt icon-inline"></i>Market Entry Strategy</h2>
        <div class="phase-timeline">
          <div class="phase">
            <div class="phase-dot">1</div>
            <div class="phase-title">Foundation</div>
            <div class="phase-details">
              Year 1-2<br>
              100K users<br>
              English markets<br>
              $5M ARR
            </div>
          </div>
          <div class="phase">
            <div class="phase-dot">2</div>
            <div class="phase-title">Expansion</div>
            <div class="phase-details">
              Year 3-4<br>
              1M users<br>
              10 languages<br>
              $50M ARR
            </div>
          </div>
          <div class="phase">
            <div class="phase-dot">3</div>
            <div class="phase-title">Global Scale</div>
            <div class="phase-details">
              Year 5-7<br>
              10M learners<br>
              50+ countries<br>
              $500M ARR
            </div>
          </div>
        </div>
      </section>

      <!-- Revenue Model -->
      <section>
        <h2><i class="fas fa-dollar-sign icon-inline"></i>Revenue Model</h2>
        <div class="benefits-grid">
          <div class="benefit-card">
            <h3><i class="fas fa-gift"></i> Free Tier</h3>
            <p><strong>$0/month</strong><br>Basic AI literacy courses<br>Limited community access</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-user"></i> Premium Individual</h3>
            <p><strong>$29/month</strong><br>Full curriculum access<br>Certifications & mentorship</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-building"></i> Corporate Training</h3>
            <p><strong>$200/employee/year</strong><br>Custom programs<br>Analytics & bulk licensing</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-certificate"></i> Certification</h3>
            <p><strong>Revenue share</strong><br>Co-branded credentials<br>Industry partnerships</p>
          </div>
        </div>
        <div class="revenue-chart">
          <div class="revenue-bar revenue-bar-1">
            <div class="revenue-value">$5M</div>
            <div class="revenue-label">Year 2</div>
          </div>
          <div class="revenue-bar revenue-bar-2">
            <div class="revenue-value">$50M</div>
            <div class="revenue-label">Year 4</div>
          </div>
          <div class="revenue-bar revenue-bar-3">
            <div class="revenue-value">$500M</div>
            <div class="revenue-label">Year 7</div>
          </div>
        </div>
      </section>

      <!-- Impact Metrics -->
      <section>
        <h2><i class="fas fa-chart-bar icon-inline"></i>2030 Impact Goals</h2>
        <div class="highlight-box">
          <p><strong>Creating measurable social and economic impact at global scale</strong></p>
        </div>
        <div class="impact-grid">
          <div class="impact-card">
            <div class="impact-metric">50M</div>
            <div class="impact-label">Active learners globally</div>
          </div>
          <div class="impact-card">
            <div class="impact-metric">150+</div>
            <div class="impact-label">Countries served</div>
          </div>
          <div class="impact-card">
            <div class="impact-metric">40%</div>
            <div class="impact-label">From developing economies</div>
          </div>
          <div class="impact-card">
            <div class="impact-metric">60%</div>
            <div class="impact-label">Female participation</div>
          </div>
          <div class="impact-card">
            <div class="impact-metric">500K</div>
            <div class="impact-label">Career transitions</div>
          </div>
          <div class="impact-card">
            <div class="impact-metric">$25B</div>
            <div class="impact-label">Economic value created</div>
          </div>
        </div>
      </section>

      <!-- Business Metrics -->
      <section>
        <h2><i class="fas fa-tachometer-alt icon-inline"></i>Key Business Metrics</h2>
        <table>
          <thead>
            <tr>
              <th>Metric</th>
              <th>Current Industry Average</th>
              <th>EduAI Global Target</th>
              <th>Competitive Edge</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Course Completion Rate</td>
              <td>15-20%</td>
              <td>85%</td>
              <td>+325%</td>
            </tr>
            <tr>
              <td>User Satisfaction (NPS)</td>
              <td>30-40</td>
              <td>75+</td>
              <td>+87%</td>
            </tr>
            <tr>
              <td>Monthly Active Users</td>
              <td>25%</td>
              <td>65%</td>
              <td>+160%</td>
            </tr>
            <tr>
              <td>Customer Acquisition Cost</td>
              <td>$150-200</td>
              <td>$50</td>
              <td>-75%</td>
            </tr>
            <tr class="highlight-row">
              <td><strong>Annual Revenue Growth</strong></td>
              <td><strong>35%</strong></td>
              <td><strong>70%</strong></td>
              <td><strong>+100%</strong></td>
            </tr>
          </tbody>
        </table>
      </section>

      <!-- Success Stories Vision -->
      <section>
        <h2><i class="fas fa-star icon-inline"></i>Success Stories (Projected)</h2>
        <div class="success-story">
          <h3>Maria from Brazil</h3>
          <p>A 42-year-old accountant who transitioned to AI-powered financial analysis. Increased her salary by 150% and now leads digital transformation at her firm. Completed our program in Portuguese with local mentorship.</p>
        </div>
        <div class="success-story">
          <h3>Ahmed from Kenya</h3>
          <p>Rural entrepreneur who learned AI through our offline modules. Built an AI-powered agricultural advisory service serving 10,000+ farmers. Featured in Time Magazine's "AI Innovators to Watch".</p>
        </div>
        <div class="success-story">
          <h3>Priya from India</h3>
          <p>Engineering student who specialized in AI ethics through our platform. Now works at a leading tech company shaping responsible AI policies. Mentors 50+ students monthly on our platform.</p>
        </div>
      </section>

      <!-- Risk Mitigation -->
      <section>
        <h2><i class="fas fa-shield-alt icon-inline"></i>Risk Mitigation Strategy</h2>
        <div class="benefits-grid">
          <div class="benefit-card">
            <h3><i class="fas fa-wifi-slash"></i> Technology Access</h3>
            <p><strong>Challenge:</strong> Limited internet in developing regions<br>
            <strong>Solution:</strong> Offline-first design, mobile optimization</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-language"></i> Cultural Barriers</h3>
            <p><strong>Challenge:</strong> Content relevance across cultures<br>
            <strong>Solution:</strong> Local partnerships, cultural adaptation</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-check-double"></i> Quality Control</h3>
            <p><strong>Challenge:</strong> Maintaining content quality at scale<br>
            <strong>Solution:</strong> AI verification, community moderation</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-users-cog"></i> Competition</h3>
            <p><strong>Challenge:</strong> Large EdTech players entering AI space<br>
            <strong>Solution:</strong> Focus on underserved markets, community</p>
          </div>
        </div>
      </section>

      <!-- Investment Ask -->
      <section>
        <h2><i class="fas fa-hand-holding-usd icon-inline"></i>Investment Opportunity</h2>
        <div class="highlight-box">
          <p><strong>Series A: $25 Million to democratize AI education globally</strong></p>
        </div>
        <div class="two-columns" style="margin-top: 30px;">
          <div class="column">
            <h3><i class="fas fa-chart-pie icon-inline"></i>Use of Funds</h3>
            <ul>
              <li><strong>40%</strong> - Technology & Product Development</li>
              <li><strong>25%</strong> - Content Creation & Localization</li>
              <li><strong>20%</strong> - Marketing & User Acquisition</li>
              <li><strong>10%</strong> - Operations & Infrastructure</li>
              <li><strong>5%</strong> - Legal & Compliance</li>
            </ul>
          </div>
          <div class="column">
            <h3><i class="fas fa-bullseye icon-inline"></i>Milestones</h3>
            <ul>
              <li>Launch MVP in 3 pilot markets</li>
              <li>Achieve 100K active users</li>
              <li>Establish 10 corporate partnerships</li>
              <li>Build content in 5 languages</li>
              <li>Achieve $5M ARR by Year 2</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Team -->
      <section>
        <h2><i class="fas fa-users icon-inline"></i>Leadership Team</h2>
        <p style="text-align: center; margin-bottom: 30px;">Experienced leaders in education, technology, and global scaling</p>
        <div class="benefits-grid">
          <div class="benefit-card">
            <h3><i class="fas fa-user-tie"></i> CEO (To be hired)</h3>
            <p>Seeking visionary leader with EdTech and global scaling experience</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-user-cog"></i> CTO (To be hired)</h3>
            <p>Platform architect with AI/ML and distributed systems expertise</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-user-graduate"></i> Chief Learning Officer</h3>
            <p>Education innovator with curriculum design and AI pedagogy experience</p>
          </div>
          <div class="benefit-card">
            <h3><i class="fas fa-user-chart"></i> VP Growth</h3>
            <p>Growth hacker with emerging markets and freemium model expertise</p>
          </div>
        </div>
        <div class="highlight-box" style="margin-top: 30px;">
          <p><strong>Advisory board includes leaders from Google, Coursera, Khan Academy, and UNESCO</strong></p>
        </div>
      </section>

      <!-- Final Call to Action -->
      <section class="final-slide">
        <i class="fas fa-brain icon-large" style="font-size: 4em;"></i>
        <h2>Join Us in Democratizing AI Education</h2>
        <div class="highlight-box">
          <p><strong>Together, we can bridge the global AI divide and empower millions</strong></p>
        </div>
        <div style="margin-top: 40px;">
          <p style="font-size: 1.2em; margin-bottom: 30px;">EduAI Global represents a transformational opportunity to:</p>
          <ul style="text-align: center; list-style: none; font-size: 1.1em;">
            <li class="fragment highlight-current-blue">✓ Address a $12B market growing at 45% CAGR</li>
            <li class="fragment highlight-current-blue">✓ Create lasting social impact for 50M+ learners</li>
            <li class="fragment highlight-current-blue">✓ Build the category-defining AI education platform</li>
            <li class="fragment highlight-current-blue">✓ Generate 10x returns while changing lives</li>
          </ul>
        </div>
        <p style="margin-top: 40px; font-size: 0.9em;">Contact: <EMAIL> | www.eduaiglobal.com</p>
      </section>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/dist/reveal.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/plugin/notes/notes.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/plugin/markdown/markdown.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/plugin/highlight/highlight.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/plugin/zoom/zoom.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.5.0/plugin/search/search.js"></script>

  <script>
    // Initialize Reveal.js
    let deck = new Reveal({
      hash: true,
      slideNumber: 'c/t',
      transition: 'slide',
      transitionSpeed: 'fast',
      controls: true,
      progress: true,
      center: true,
      width: 1400,
      height: 900,
      margin: 0.04,
      viewDistance: 3,
      showNotes: false,
      backgroundTransition: 'fade',
      controlsLayout: 'bottom-right',
      controlsBackArrows: 'faded',
      fragments: true,
      fragmentInURL: true,
      pdfMaxPagesPerSlide: 1,
      pdfSeparateFragments: false,
      pdfPageHeightOffset: -1,
      plugins: [ RevealMarkdown, RevealHighlight, RevealNotes, RevealZoom, RevealSearch ]
    });

    // Initialize the presentation
    deck.initialize();

    // PDF Export Button
    const pdfButton = document.createElement('div');
    pdfButton.className = 'custom-action-btn';
    pdfButton.title = 'Export to PDF (Use Browser Print: Ctrl+P)';
    pdfButton.style.right = '170px';
    pdfButton.innerHTML = `<svg width="26" height="26" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="4" y="2" width="16" height="20" rx="2" fill="#fff" stroke="#1a1a2e" stroke-width="2"/><path d="M8 6h8M8 10h8M8 14h4" stroke="#1a1a2e" stroke-width="2" stroke-linecap="round"/></svg>`;
    pdfButton.addEventListener('click', function() {
      const currentUrl = window.location.href.split('?')[0];
      window.open(currentUrl + '?print-pdf', '_blank');
      setTimeout(() => {
        alert("Presentation opened in PDF mode in a new tab. Use your browser's Print function (Ctrl+P or Cmd+P) and select 'Save as PDF'.");
      }, 500);
    });

    // Fullscreen Button
    const fullscreenButton = document.createElement('div');
    fullscreenButton.className = 'custom-action-btn';
    fullscreenButton.title = 'Toggle Fullscreen';
    fullscreenButton.style.right = '120px';
    fullscreenButton.innerHTML = `<svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 4h6M4 4v6M4 20h6M4 20v-6M20 4h-6M20 4v6M20 20h-6M20 20v-6" stroke="#1a1a2e" stroke-width="2" stroke-linecap="round"/></svg>`;
    fullscreenButton.addEventListener('click', function() {
      const elem = document.documentElement;
      if (!document.fullscreenElement) {
        if (elem.requestFullscreen) elem.requestFullscreen();
      } else {
        if (document.exitFullscreen) document.exitFullscreen();
      }
    });

    // Only add the buttons if not already in print mode
    var isPrintPDF = window.location.search.match(/print-pdf/gi);
    if (!isPrintPDF) {
      document.body.appendChild(pdfButton);
      document.body.appendChild(fullscreenButton);
    }
  </script>
</body>
</html>