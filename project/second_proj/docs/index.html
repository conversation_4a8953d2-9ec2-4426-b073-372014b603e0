<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>EduAI Global - Democratizing AI Education</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/black.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .reveal {
            font-family: 'Arial', sans-serif;
        }
        
        .reveal .slides section {
            text-align: left;
        }
        
        .reveal h1, .reveal h2, .reveal h3 {
            color: #ff0000;
            text-transform: none;
            font-weight: bold;
        }
        
        .reveal h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #ff0000, #cc0000);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
        }
        
        .hero-slide {
            text-align: center !important;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            padding: 2em;
            border-radius: 20px;
        }
        
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2em;
            margin: 2em 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            padding: 1.5em;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #fff;
            margin-bottom: 0.2em;
        }
        
        .stat-label {
            font-size: 1em;
            color: #e5e7eb;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5em;
            margin: 2em 0;
        }
        
        .feature-card {
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid #ff0000;
            padding: 1.5em;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 0, 0, 0.2);
        }
        
        .feature-icon {
            font-size: 2em;
            color: #ff0000;
            margin-bottom: 0.5em;
        }
        
        .phase-timeline {
            display: flex;
            justify-content: space-between;
            margin: 2em 0;
            position: relative;
        }
        
        .phase-timeline::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff0000, #cc0000, #aa0000);
            z-index: -1;
        }
        
        .phase {
            background: #1a1a2e;
            border: 3px solid #ff0000;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: relative;
        }
        
        .phase-1 { border-color: #ff0000; }
        .phase-2 { border-color: #cc0000; }
        .phase-3 { border-color: #aa0000; }
        
        .revenue-chart {
            display: flex;
            align-items: end;
            justify-content: space-around;
            height: 300px;
            margin: 2em 0;
            padding: 1em;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }
        
        .revenue-bar {
            background: linear-gradient(180deg, #ff0000, #cc0000);
            border-radius: 5px 5px 0 0;
            min-width: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: end;
            color: white;
            font-weight: bold;
            position: relative;
        }
        
        .revenue-bar-1 { height: 15%; }
        .revenue-bar-2 { height: 50%; }
        .revenue-bar-3 { height: 100%; }
        
        .revenue-label {
            position: absolute;
            bottom: -2em;
            font-size: 0.9em;
        }
        
        .revenue-value {
            position: absolute;
            top: -2em;
            font-size: 0.8em;
        }
        
        .impact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5em;
            margin: 2em 0;
        }
        
        .impact-card {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            padding: 1.5em;
            border-radius: 15px;
            text-align: center;
            color: white;
        }
        
        .cta-slide {
            text-align: center !important;
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 50%, #aa0000 100%);
            padding: 3em;
            border-radius: 20px;
            color: white;
        }
        
        .cta-button {
            background: white;
            color: #ff0000;
            padding: 1em 2em;
            border-radius: 50px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 1em;
            transition: transform 0.3s ease;
        }
        
        .cta-button:hover {
            transform: scale(1.05);
            color: #ff0000;
        }
        
        .slide-number {
            color: #ff0000 !important;
        }
        
        .progress {
            background: #ff0000 !important;
        }
        
        ul {
            list-style: none;
            padding: 0;
        }
        
        li {
            margin: 0.8em 0;
            padding-left: 1.5em;
            position: relative;
        }
        
        li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #ff0000;
            font-weight: bold;
        }
        
        .highlight {
            color: #ff0000;
            font-weight: bold;
        }
        
        .center {
            text-align: center !important;
        }
    </style>
</head>

<body>
    <div class="reveal">
        <div class="slides">
            
            <!-- Title Slide -->
            <section class="hero-slide">
                <h1><i class="fas fa-brain"></i> EduAI Global</h1>
                <h2 style="color: #e5e7eb; margin-top: 1em;">Democratizing AI Education for All</h2>
                <p style="color: #9ca3af; font-size: 1.2em; margin-top: 2em;">
                    Making comprehensive AI education accessible to every person on Earth
                </p>
                <div style="margin-top: 3em;">
                    <i class="fas fa-globe-americas" style="font-size: 3em; color: #ff0000;"></i>
                </div>
            </section>

            <!-- Mission & Vision -->
            <section>
                <h2><i class="fas fa-rocket"></i> Our Mission & Vision</h2>
                <div style="margin: 2em 0;">
                    <div style="background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%); padding: 2em; border-radius: 15px; margin-bottom: 1.5em;">
                        <h3 style="color: white; margin-bottom: 1em;"><i class="fas fa-bullseye"></i> Mission</h3>
                        <p style="color: #e5e7eb; font-size: 1.1em;">
                            To make comprehensive AI education accessible to every person on Earth, regardless of geographic location, economic status, or educational background.
                        </p>
                    </div>
                    <div style="background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%); padding: 2em; border-radius: 15px;">
                        <h3 style="color: white; margin-bottom: 1em;"><i class="fas fa-eye"></i> Vision 2030</h3>
                        <p style="color: #e5e7eb; font-size: 1.1em;">
                            Be the world's leading platform for AI literacy, serving <span class="highlight" style="color: #ff0000;">50 million active learners</span> across <span class="highlight" style="color: #ff0000;">150+ countries</span>
                        </p>
                    </div>
                </div>
            </section>

            <!-- Market Opportunity -->
            <section>
                <h2><i class="fas fa-chart-line"></i> The Global AI Skills Crisis</h2>
                <div class="stat-grid">
                    <div class="stat-card">
                        <div class="stat-number">12%</div>
                        <div class="stat-label">Global workforce with basic AI literacy</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">375M</div>
                        <div class="stat-label">Jobs requiring AI reskilling by 2030</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">$15.7T</div>
                        <div class="stat-label">Potential economic impact of AI by 2030</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">85%</div>
                        <div class="stat-label">Developing nations lack AI education programs</div>
                    </div>
                </div>
            </section>

            <!-- Market Size -->
            <section>
                <h2><i class="fas fa-globe"></i> Market Opportunity</h2>
                <div style="margin: 2em 0;">
                    <div style="background: rgba(255, 0, 0, 0.1); border-left: 5px solid #ff0000; padding: 1.5em; margin: 1em 0;">
                        <h3>Total Addressable Market</h3>
                        <p style="font-size: 1.5em; color: #ff0000; font-weight: bold;">$340 billion</p>
                        <p>Global education technology market</p>
                    </div>
                    <div style="background: rgba(255, 0, 0, 0.1); border-left: 5px solid #cc0000; padding: 1.5em; margin: 1em 0;">
                        <h3>Serviceable Addressable Market</h3>
                        <p style="font-size: 1.5em; color: #ff0000; font-weight: bold;">$89 billion</p>
                        <p>Professional/adult learning segment</p>
                    </div>
                    <div style="background: rgba(255, 0, 0, 0.1); border-left: 5px solid #aa0000; padding: 1.5em; margin: 1em 0;">
                        <h3>Serviceable Obtainable Market</h3>
                        <p style="font-size: 1.5em; color: #ff0000; font-weight: bold;">$12 billion</p>
                        <p>AI-focused education market</p>
                    </div>
                </div>
            </section>

            <!-- Target Audiences -->
            <section>
                <h2><i class="fas fa-users"></i> Primary Audiences</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-briefcase"></i></div>
                        <h3>Working Professionals</h3>
                        <p>35-50 years<br>Career transition & upskilling</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-graduation-cap"></i></div>
                        <h3>University Students</h3>
                        <p>18-25 years<br>Career preparation & specialization</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-lightbulb"></i></div>
                        <h3>Entrepreneurs & SME Owners</h3>
                        <p>25-45 years<br>Business AI integration</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                        <h3>Educators & Trainers</h3>
                        <p>28-55 years<br>Teaching AI concepts to others</p>
                    </div>
                </div>
            </section>

            <!-- Key Features -->
            <section>
                <h2><i class="fas fa-star"></i> Platform Features</h2>
                <div style="margin: 2em 0;">
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li><strong class="highlight">Personalized Learning Paths:</strong> AI-powered curriculum adaptation</li>
                        <li><strong class="highlight">Practical Project Portfolio:</strong> Real-world AI implementations</li>
                        <li><strong class="highlight">Global Community Network:</strong> Peer learning across continents</li>
                        <li><strong class="highlight">Industry Certification:</strong> Recognized credentials from tech leaders</li>
                        <li><strong class="highlight">Multi-Language Support:</strong> Content in 25+ languages</li>
                        <li><strong class="highlight">Offline Capability:</strong> Learning without internet dependency</li>
                    </ul>
                </div>
                <div class="center" style="margin-top: 2em;">
                    <p style="color: #ff0000; font-size: 1.3em; font-weight: bold;">
                        "The Netflix of AI Education"
                    </p>
                </div>
            </section>

            <!-- Competitive Advantage -->
            <section>
                <h2><i class="fas fa-trophy"></i> Unique Differentiators</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-globe-asia"></i></div>
                        <h3>Cultural Relevance</h3>
                        <p>AI applications tailored to local industries and challenges</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-coins"></i></div>
                        <h3>Economic Accessibility</h3>
                        <p>Sliding scale pricing based on purchasing power parity</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-hands-helping"></i></div>
                        <h3>Mentorship Network</h3>
                        <p>1:10 mentor-to-student ratio with industry experts</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-building"></i></div>
                        <h3>Real-World Application</h3>
                        <p>Partnership with 500+ companies for practical projects</p>
                    </div>
                </div>
            </section>

            <!-- Market Entry Strategy -->
            <section>
                <h2><i class="fas fa-map-marked-alt"></i> Market Entry Strategy</h2>
                <div class="phase-timeline">
                    <div class="phase phase-1">
                        <div style="font-size: 0.8em;">Year 1-2</div>
                        <div style="font-size: 1.2em; color: #ff0000;">Foundation</div>
                    </div>
                    <div class="phase phase-2">
                        <div style="font-size: 0.8em;">Year 3-4</div>
                        <div style="font-size: 1.2em; color: #cc0000;">Expansion</div>
                    </div>
                    <div class="phase phase-3">
                        <div style="font-size: 0.8em;">Year 5-7</div>
                        <div style="font-size: 1.2em; color: #aa0000;">Global</div>
                    </div>
                </div>
                <div class="feature-grid" style="margin-top: 3em;">
                    <div style="background: rgba(255, 0, 0, 0.1); padding: 1.5em; border-radius: 10px;">
                        <h3 style="color: #ff0000;">Phase 1: Foundation</h3>
                        <ul style="font-size: 0.9em;">
                            <li>100,000 users</li>
                            <li>English markets</li>
                            <li>$5M ARR</li>
                        </ul>
                    </div>
                    <div style="background: rgba(255, 0, 0, 0.1); padding: 1.5em; border-radius: 10px;">
                        <h3 style="color: #cc0000;">Phase 2: Expansion</h3>
                        <ul style="font-size: 0.9em;">
                            <li>1M active users</li>
                            <li>10 languages</li>
                            <li>$50M ARR</li>
                        </ul>
                    </div>
                    <div style="background: rgba(255, 0, 0, 0.1); padding: 1.5em; border-radius: 10px;">
                        <h3 style="color: #aa0000;">Phase 3: Global</h3>
                        <ul style="font-size: 0.9em;">
                            <li>10M learners</li>
                            <li>50+ countries</li>
                            <li>$500M ARR</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Revenue Model -->
            <section>
                <h2><i class="fas fa-dollar-sign"></i> Revenue Model</h2>
                <div class="feature-grid">
                    <div style="background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%); padding: 1.5em; border-radius: 15px; color: white;">
                        <h3><i class="fas fa-gift"></i> Free Tier</h3>
                        <p>Basic AI literacy courses, limited community access</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #cc0000 0%, #aa0000 100%); padding: 1.5em; border-radius: 15px; color: white;">
                        <h3><i class="fas fa-user"></i> Premium Individual</h3>
                        <p><strong>$29/month</strong><br>Full curriculum, certifications, mentorship</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #aa0000 0%, #880000 100%); padding: 1.5em; border-radius: 15px; color: white;">
                        <h3><i class="fas fa-building"></i> Corporate Training</h3>
                        <p><strong>$200/employee/year</strong><br>Custom programs, analytics, bulk licensing</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%); padding: 1.5em; border-radius: 15px; color: white;">
                        <h3><i class="fas fa-certificate"></i> Certification Partnership</h3>
                        <p><strong>Revenue share</strong><br>Co-branded credentials with industry leaders</p>
                    </div>
                </div>
                <div class="revenue-chart">
                    <div class="revenue-bar revenue-bar-1">
                        <div class="revenue-value">$5M</div>
                        <div class="revenue-label">Year 1-2</div>
                    </div>
                    <div class="revenue-bar revenue-bar-2">
                        <div class="revenue-value">$50M</div>
                        <div class="revenue-label">Year 3-4</div>
                    </div>
                    <div class="revenue-bar revenue-bar-3">
                        <div class="revenue-value">$500M</div>
                        <div class="revenue-label">Year 5-7</div>
                    </div>
                </div>
            </section>

            <!-- Technology Infrastructure -->
            <section>
                <h2><i class="fas fa-server"></i> Technology Infrastructure</h2>
                <div style="margin: 2em 0;">
                    <div style="background: rgba(255, 0, 0, 0.1); border-left: 5px solid #ff0000; padding: 1.5em; margin: 1em 0;">
                        <h3><i class="fas fa-cloud"></i> Learning Management System</h3>
                        <ul style="font-size: 0.9em;">
                            <li>Cloud-first architecture with edge computing</li>
                            <li>AI-powered recommendation engine</li>
                            <li>Advanced analytics and outcome prediction</li>
                            <li>Mobile-first design with PWA capabilities</li>
                        </ul>
                    </div>
                    <div style="background: rgba(255, 0, 0, 0.1); border-left: 5px solid #cc0000; padding: 1.5em; margin: 1em 0;">
                        <h3><i class="fas fa-play-circle"></i> Content Delivery</h3>
                        <ul style="font-size: 0.9em;">
                            <li>Interactive simulations and virtual AI labs</li>
                            <li>AR/VR components for immersive learning</li>
                            <li>Live coding environments and AI model playgrounds</li>
                            <li>Collaborative workspace for team projects</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Impact Metrics -->
            <section>
                <h2><i class="fas fa-chart-bar"></i> Impact Metrics & Goals 2030</h2>
                <div class="impact-grid">
                    <div class="impact-card">
                        <h3 style="font-size: 2em; margin-bottom: 0.5em;">50M</h3>
                        <p>Learners across 150+ countries</p>
                    </div>
                    <div class="impact-card">
                        <h3 style="font-size: 2em; margin-bottom: 0.5em;">40%</h3>
                        <p>Users from developing economies</p>
                    </div>
                    <div class="impact-card">
                        <h3 style="font-size: 2em; margin-bottom: 0.5em;">60%</h3>
                        <p>Female participation in AI education</p>
                    </div>
                    <div class="impact-card">
                        <h3 style="font-size: 2em; margin-bottom: 0.5em;">500K</h3>
                        <p>Career transitions facilitated</p>
                    </div>
                    <div class="impact-card">
                        <h3 style="font-size: 2em; margin-bottom: 0.5em;">$25B</h3>
                        <p>Economic value created</p>
                    </div>
                    <div class="impact-card">
                        <h3 style="font-size: 2em; margin-bottom: 0.5em;">85%</h3>
                        <p>Course completion rate</p>
                    </div>
                </div>
            </section>

            <!-- Risk Mitigation -->
            <section>
                <h2><i class="fas fa-shield-alt"></i> Risk Mitigation</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-wifi"></i></div>
                        <h3>Technology Access</h3>
                        <p><strong>Solution:</strong> Offline-first design and mobile optimization</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-globe"></i></div>
                        <h3>Cultural Barriers</h3>
                        <p><strong>Solution:</strong> Local partnerships and culturally relevant content</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-check-circle"></i></div>
                        <h3>Quality Control</h3>
                        <p><strong>Solution:</strong> AI-powered content verification and community moderation</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon"><i class="fas fa-trophy"></i></div>
                        <h3>Competition</h3>
                        <p><strong>Solution:</strong> Focus on underserved markets and community-driven differentiation</p>
                    </div>
                </div>
            </section>

            <!-- Call to Action -->
            <section class="cta-slide">
                <h1><i class="fas fa-rocket"></i> Ready to Transform AI Education?</h1>
                <p style="font-size: 1.3em; margin: 2em 0;">
                    Join us in democratizing the most important skill of the 21st century
                </p>
                <div style="margin: 2em 0;">
                    <h3>Next Steps:</h3>
                    <ul style="text-align: left; display: inline-block; font-size: 1.1em;">
                        <li>Secure Series A funding ($25 million)</li>
                        <li>Build core team of 50 engineers & educators</li>
                        <li>Launch MVP in 3 pilot markets</li>
                        <li>Establish first 10 corporate partnerships</li>
                    </ul>
                </div>
                <div style="margin-top: 3em;">
                    <a href="#" class="cta-button">Let's Build the Future</a>
                    <a href="#" class="cta-button">Join Our Mission</a>
                </div>
                <p style="font-size: 1.5em; margin-top: 2em; font-weight: bold;">
                    Together, we can make AI education truly accessible to all.
                </p>
            </section>

        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
    <script>
        Reveal.initialize({
            hash: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            center: true,
            touch: true,
            loop: false,
            rtl: false,
            shuffle: false,
            fragments: true,
            help: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoSlide: 0,
            autoSlideStoppable: true,
            mouseWheel: false,
            hideInactiveCursor: true,
            hideCursorTime: 5000,
            previewLinks: false,
            focusBodyOnPageVisibilityChange: true,
            viewDistance: 3,
            mobileViewDistance: 2,
            display: 'block',
            hideAddressBar: true,
            parallaxBackgroundImage: '',
            parallaxBackgroundSize: '',
            parallaxBackgroundRepeat: '',
            parallaxBackgroundPosition: '',
            parallaxBackgroundHorizontal: null,
            parallaxBackgroundVertical: null
        });
    </script>
</body>
</html>