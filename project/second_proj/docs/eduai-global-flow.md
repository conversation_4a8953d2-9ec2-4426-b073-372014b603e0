# EduAI Global Project Flow

```mermaid
%%{init: {
  'theme': 'base',
  'themeVariables': {
    'fontFamily': 'Arial, sans-serif',
    'fontSize': '14px',
    'primaryTextColor': '#2A2A2A'
  }
}}%%
flowchart TD
    %% Styling Definitions
    classDef mission fill:#EBF5FF,stroke:#2C5282,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef users fill:#FFF5F5,stroke:#C53030,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef features fill:#F0FFF4,stroke:#2F855A,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef business fill:#FEF9E7,stroke:#B7950B,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef growth fill:#E8DAEF,stroke:#76448A,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef metrics fill:#D4EFDF,stroke:#229954,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef tech fill:#D6EAF8,stroke:#2874A6,stroke-width:2px,color:#2D3748,font-family:Arial,font-size:14px,padding:10px;
    classDef subgraphStyle fill:#F7FAFC,stroke:#E2E8F0,stroke-width:2px,color:#4A5568,font-family:Arial,font-size:16px,font-weight:bold;

    %% Mission
    subgraph Mission["🌍 Mission"]
        A["Democratize AI education globally\nthrough an accessible online platform"]
    end

    %% Users
    subgraph Users["👥 Target Users"]
        B1["Working professionals (35-50)"]
        B2["University students (18-25)"]
        B3["Entrepreneurs"]
        B4["Educators"]
    end

    %% Features
    subgraph Features["✨ Core Features"]
        C1["Personalized AI-powered learning paths"]
        C2["Practical project portfolios"]
        C3["Global community & mentorship"]
        C4["Industry certifications"]
        C5["Multi-language support (25+)"]
        C6["Offline capability"]
    end

    %% Business Model
    subgraph Business["💼 Business Model"]
        D1["Free Tier: Basic courses"]
        D2["Premium: $29/mo, full curriculum"]
        D3["Corporate: $200/employee/year"]
        D4["Partnerships: Revenue-share"]
    end

    %% Growth Phases
    subgraph Growth["📈 Growth Strategy"]
        E1["Phase 1: 100K users, $5M ARR"]
        E2["Phase 2: 1M users, $50M ARR"]
        E3["Phase 3: 10M users, $500M ARR"]
    end

    %% Key Metrics
    subgraph Metrics["📊 Key Metrics"]
        F1["50M learners by 2030"]
        F2["85% course completion"]
        F3["40% from developing economies"]
        F4["60% female participation"]
    end

    %% Tech Stack
    subgraph Tech["🛠️ Tech Stack"]
        G1["Cloud-first LMS"]
        G2["AI recommendation engine"]
        G3["Mobile-first PWA"]
        G4["Simulations & virtual labs"]
        G5["AR/VR learning"]
    end

    %% Flow Connections
    A --> B1
    A --> B2
    A --> B3
    A --> B4
    B1 --> C1
    B2 --> C1
    B3 --> C1
    B4 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> C5
    C5 --> C6
    C6 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> E1
    E1 --> E2
    E2 --> E3
    E3 --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
    F4 --> G1
    G1 --> G2
    G2 --> G3
    G3 --> G4
    G4 --> G5

    %% Apply Classes
    class A mission;
    class B1,B2,B3,B4 users;
    class C1,C2,C3,C4,C5,C6 features;
    class D1,D2,D3,D4 business;
    class E1,E2,E3 growth;
    class F1,F2,F3,F4 metrics;
    class G1,G2,G3,G4,G5 tech;
    class Mission,Users,Features,Business,Growth,Metrics,Tech subgraphStyle;
``` 