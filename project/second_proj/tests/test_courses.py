"""
Tests for course management endpoints and functionality.
"""
import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from fastapi import status
from uuid import uuid4


class TestCourseManagement:
    """Test course management API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_course_admin(self, async_client, db_session, admin_auth_headers, sample_course_data):
        """Test admin creating a new course (TEST-BE-005)."""
        # Act
        response = await async_client.post(
            "/api/v1/courses",
            json=sample_course_data,
            headers=admin_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "id" in data
        assert data["title"]["en"] == sample_course_data["title"]["en"]
        assert data["title"]["es"] == sample_course_data["title"]["es"]
        assert data["difficulty_level"] == sample_course_data["difficulty_level"]
        assert data["estimated_hours"] == sample_course_data["estimated_hours"]
        assert data["tags"] == sample_course_data["tags"]
        assert data["is_premium"] == sample_course_data["is_premium"]
        assert "created_at" in data
        assert "updated_at" in data
    
    @pytest.mark.asyncio
    async def test_create_course_non_admin(self, async_client, db_session, auth_headers, sample_course_data):
        """Test non-admin user cannot create course."""
        # Act
        response = await async_client.post(
            "/api/v1/courses",
            json=sample_course_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        data = response.json()
        assert "detail" in data
        assert "permission" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_create_course_invalid_data(self, async_client, db_session, admin_auth_headers):
        """Test course creation with invalid data."""
        # Arrange
        invalid_data = {
            "title": "Not a multilanguage object",  # Should be dict
            "difficulty_level": "super_hard",  # Invalid enum
            "estimated_hours": -5  # Negative hours
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/courses",
            json=invalid_data,
            headers=admin_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
    
    @pytest.mark.asyncio
    async def test_list_courses(self, async_client, db_session):
        """Test listing courses with filters (TEST-BE-006)."""
        # Arrange - Create test courses
        # In real implementation, you'd create actual courses in db
        
        # Act
        response = await async_client.get(
            "/api/v1/courses",
            params={
                "difficulty": "beginner",
                "language": "en",
                "limit": 10,
                "offset": 0
            }
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "limit" in data
        assert "offset" in data
        assert isinstance(data["items"], list)
        
        # All returned courses should match filters
        for course in data["items"]:
            assert course["difficulty_level"] == "beginner"
            assert "en" in course["title"]
    
    @pytest.mark.asyncio
    async def test_list_courses_pagination(self, async_client, db_session):
        """Test course listing pagination."""
        # Act - First page
        response1 = await async_client.get(
            "/api/v1/courses",
            params={"limit": 5, "offset": 0}
        )
        
        # Act - Second page
        response2 = await async_client.get(
            "/api/v1/courses",
            params={"limit": 5, "offset": 5}
        )
        
        # Assert
        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        
        data1 = response1.json()
        data2 = response2.json()
        
        assert len(data1["items"]) <= 5
        assert len(data2["items"]) <= 5
        assert data1["limit"] == 5
        assert data2["offset"] == 5
    
    @pytest.mark.asyncio
    async def test_search_courses(self, async_client, db_session):
        """Test course search functionality."""
        # Act
        response = await async_client.get(
            "/api/v1/courses",
            params={"search": "machine learning"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All results should be relevant to search term
        for course in data["items"]:
            title_match = any("machine" in title.lower() or "learning" in title.lower() 
                            for title in course["title"].values())
            tag_match = any("machine" in tag or "learning" in tag 
                          for tag in course.get("tags", []))
            assert title_match or tag_match
    
    @pytest.mark.asyncio
    async def test_get_course_by_id(self, async_client, db_session, test_course):
        """Test getting a specific course by ID."""
        # Act
        response = await async_client.get(f"/api/v1/courses/{test_course['id']}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == test_course["id"]
        assert data["title"] == test_course["title"]
    
    @pytest.mark.asyncio
    async def test_get_course_not_found(self, async_client, db_session):
        """Test getting non-existent course."""
        # Arrange
        fake_id = str(uuid4())
        
        # Act
        response = await async_client.get(f"/api/v1/courses/{fake_id}")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_update_course(self, async_client, db_session, admin_auth_headers, test_course):
        """Test updating course details."""
        # Arrange
        update_data = {
            "title": {"en": "Updated AI Course", "es": "Curso de IA Actualizado"},
            "estimated_hours": 15
        }
        
        # Act
        response = await async_client.patch(
            f"/api/v1/courses/{test_course['id']}",
            json=update_data,
            headers=admin_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["title"]["en"] == update_data["title"]["en"]
        assert data["estimated_hours"] == update_data["estimated_hours"]
        assert "updated_at" in data
    
    @pytest.mark.asyncio
    async def test_delete_course(self, async_client, db_session, admin_auth_headers, test_course):
        """Test deleting a course."""
        # Act
        response = await async_client.delete(
            f"/api/v1/courses/{test_course['id']}",
            headers=admin_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Verify course is deleted
        get_response = await async_client.get(f"/api/v1/courses/{test_course['id']}")
        assert get_response.status_code == status.HTTP_404_NOT_FOUND


class TestCourseEnrollment:
    """Test course enrollment functionality."""
    
    @pytest.mark.asyncio
    async def test_enroll_in_course(self, async_client, db_session, auth_headers, test_course):
        """Test user enrolling in a course (TEST-BE-007)."""
        # Act
        response = await async_client.post(
            f"/api/v1/courses/{test_course['id']}/enroll",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "enrollment_id" in data
        assert "course_id" in data
        assert "user_id" in data
        assert "enrolled_at" in data
        assert "progress" in data
        assert data["progress"]["completion_percentage"] == 0
    
    @pytest.mark.asyncio
    async def test_enroll_duplicate(self, async_client, db_session, auth_headers, test_course):
        """Test preventing duplicate enrollment."""
        # Arrange - First enrollment
        await async_client.post(
            f"/api/v1/courses/{test_course['id']}/enroll",
            headers=auth_headers
        )
        
        # Act - Try to enroll again
        response = await async_client.post(
            f"/api/v1/courses/{test_course['id']}/enroll",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_409_CONFLICT
        data = response.json()
        assert "detail" in data
        assert "already enrolled" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_enroll_premium_course_free_user(self, async_client, db_session, auth_headers):
        """Test free user cannot enroll in premium course."""
        # Arrange - Create premium course
        premium_course = {
            "id": str(uuid4()),
            "title": {"en": "Premium AI Course"},
            "is_premium": True
        }
        
        # Act
        response = await async_client.post(
            f"/api/v1/courses/{premium_course['id']}/enroll",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        data = response.json()
        assert "detail" in data
        assert "premium" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_get_enrolled_courses(self, async_client, db_session, auth_headers):
        """Test getting user's enrolled courses."""
        # Act
        response = await async_client.get(
            "/api/v1/users/me/courses",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert isinstance(data["items"], list)
        
        # Each course should have enrollment info
        for course in data["items"]:
            assert "course" in course
            assert "enrollment" in course
            assert "progress" in course
    
    @pytest.mark.asyncio
    async def test_unenroll_from_course(self, async_client, db_session, auth_headers, test_course):
        """Test unenrolling from a course."""
        # Arrange - Enroll first
        await async_client.post(
            f"/api/v1/courses/{test_course['id']}/enroll",
            headers=auth_headers
        )
        
        # Act
        response = await async_client.delete(
            f"/api/v1/courses/{test_course['id']}/enroll",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Verify enrollment removed
        enrolled_response = await async_client.get(
            "/api/v1/users/me/courses",
            headers=auth_headers
        )
        enrolled_data = enrolled_response.json()
        course_ids = [item["course"]["id"] for item in enrolled_data["items"]]
        assert test_course["id"] not in course_ids


class TestCourseModules:
    """Test course module management."""
    
    @pytest.mark.asyncio
    async def test_create_module(self, async_client, db_session, admin_auth_headers, test_course, sample_module_data):
        """Test creating a module for a course."""
        # Arrange
        sample_module_data["course_id"] = test_course["id"]
        
        # Act
        response = await async_client.post(
            f"/api/v1/courses/{test_course['id']}/modules",
            json=sample_module_data,
            headers=admin_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "id" in data
        assert data["course_id"] == test_course["id"]
        assert data["title"]["en"] == sample_module_data["title"]["en"]
        assert data["content_type"] == sample_module_data["content_type"]
        assert data["duration"] == sample_module_data["duration"]
        assert data["order_index"] == sample_module_data["order_index"]
    
    @pytest.mark.asyncio
    async def test_list_course_modules(self, async_client, db_session, test_course):
        """Test listing modules for a course."""
        # Act
        response = await async_client.get(
            f"/api/v1/courses/{test_course['id']}/modules"
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        
        # Modules should be ordered by order_index
        if len(data) > 1:
            for i in range(1, len(data)):
                assert data[i]["order_index"] >= data[i-1]["order_index"]
    
    @pytest.mark.asyncio
    async def test_get_module_content(self, async_client, db_session, auth_headers, test_course):
        """Test getting module content (enrolled user)."""
        # Arrange - Create module and enroll user
        module_id = str(uuid4())
        
        # Act
        response = await async_client.get(
            f"/api/v1/modules/{module_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "id" in data
        assert "content_url" in data
        assert "content_data" in data
    
    @pytest.mark.asyncio
    async def test_get_module_content_not_enrolled(self, async_client, db_session, auth_headers):
        """Test getting module content without enrollment."""
        # Arrange
        module_id = str(uuid4())
        
        # Act
        response = await async_client.get(
            f"/api/v1/modules/{module_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        data = response.json()
        assert "detail" in data
        assert "not enrolled" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_update_module_order(self, async_client, db_session, admin_auth_headers, test_course):
        """Test reordering course modules."""
        # Arrange
        reorder_data = {
            "modules": [
                {"id": "module1", "order_index": 2},
                {"id": "module2", "order_index": 1},
                {"id": "module3", "order_index": 3}
            ]
        }
        
        # Act
        response = await async_client.patch(
            f"/api/v1/courses/{test_course['id']}/modules/reorder",
            json=reorder_data,
            headers=admin_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert "reordered" in data["message"].lower()


class TestCourseCategories:
    """Test course categorization and tagging."""
    
    @pytest.mark.asyncio
    async def test_list_course_categories(self, async_client, db_session):
        """Test listing available course categories."""
        # Act
        response = await async_client.get("/api/v1/courses/categories")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Each category should have required fields
        for category in data:
            assert "id" in category
            assert "name" in category
            assert "slug" in category
            assert "course_count" in category
    
    @pytest.mark.asyncio
    async def test_filter_courses_by_tag(self, async_client, db_session):
        """Test filtering courses by tags."""
        # Act
        response = await async_client.get(
            "/api/v1/courses",
            params={"tags": ["machine-learning", "beginner"]}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All courses should have at least one of the tags
        for course in data["items"]:
            course_tags = course.get("tags", [])
            assert any(tag in course_tags for tag in ["machine-learning", "beginner"])


class TestCourseReviews:
    """Test course review functionality."""
    
    @pytest.mark.asyncio
    async def test_add_course_review(self, async_client, db_session, auth_headers, test_course):
        """Test adding a review to a course."""
        # Arrange
        review_data = {
            "rating": 5,
            "title": "Excellent Course!",
            "content": "This course helped me understand AI concepts clearly.",
            "would_recommend": True
        }
        
        # Act
        response = await async_client.post(
            f"/api/v1/courses/{test_course['id']}/reviews",
            json=review_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "id" in data
        assert data["rating"] == review_data["rating"]
        assert data["title"] == review_data["title"]
        assert data["content"] == review_data["content"]
        assert "user" in data
        assert "created_at" in data
    
    @pytest.mark.asyncio
    async def test_get_course_reviews(self, async_client, db_session, test_course):
        """Test getting reviews for a course."""
        # Act
        response = await async_client.get(
            f"/api/v1/courses/{test_course['id']}/reviews"
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "average_rating" in data
        assert "rating_distribution" in data
    
    @pytest.mark.asyncio
    async def test_update_own_review(self, async_client, db_session, auth_headers, test_course):
        """Test updating own review."""
        # Arrange - Create review first
        create_response = await async_client.post(
            f"/api/v1/courses/{test_course['id']}/reviews",
            json={"rating": 4, "content": "Good course"},
            headers=auth_headers
        )
        review_id = create_response.json()["id"]
        
        # Act
        update_data = {"rating": 5, "content": "Actually, it's excellent!"}
        response = await async_client.patch(
            f"/api/v1/reviews/{review_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["rating"] == update_data["rating"]
        assert data["content"] == update_data["content"]
        assert "updated_at" in data


class TestCourseRecommendations:
    """Test course recommendation features."""
    
    @pytest.mark.asyncio
    async def test_get_similar_courses(self, async_client, db_session, test_course):
        """Test getting similar course recommendations."""
        # Act
        response = await async_client.get(
            f"/api/v1/courses/{test_course['id']}/similar"
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 10  # Reasonable limit
        
        # Similar courses should not include the original course
        course_ids = [course["id"] for course in data]
        assert test_course["id"] not in course_ids
    
    @pytest.mark.asyncio
    async def test_get_course_prerequisites(self, async_client, db_session, test_course):
        """Test getting course prerequisites."""
        # Act
        response = await async_client.get(
            f"/api/v1/courses/{test_course['id']}/prerequisites"
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        
        # Each prerequisite should have course info
        for prereq in data:
            assert "id" in prereq
            assert "title" in prereq
            assert "difficulty_level" in prereq
            assert "estimated_hours" in prereq
