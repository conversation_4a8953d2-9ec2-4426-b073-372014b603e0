"""
Tests for authentication endpoints and functionality.
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import jwt
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession


class TestAuthenticationEndpoints:
    """Test authentication API endpoints."""
    
    @pytest.mark.asyncio
    async def test_user_registration_success(self, async_client, db_session, sample_user_data):
        """Test successful user registration (TEST-BE-001)."""
        # Act
        response = await async_client.post(
            "/api/v1/auth/register",
            json=sample_user_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["email"] == sample_user_data["email"]
        assert data["user"]["name"] == sample_user_data["name"]
        assert "password" not in data["user"]
        
        # Verify token is valid
        token = data["access_token"]
        # In real implementation, decode and verify the token
        # decoded = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        # assert decoded["sub"] == data["user"]["id"]
    
    @pytest.mark.asyncio
    async def test_user_registration_duplicate_email(self, async_client, db_session, test_user):
        """Test duplicate email registration prevention (TEST-BE-002)."""
        # Arrange
        registration_data = {
            "email": test_user["email"],  # Using existing user's email
            "password": "AnotherPassword123!",
            "name": "Another User",
            "preferred_language": "en"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/register",
            json=registration_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_409_CONFLICT
        data = response.json()
        assert "detail" in data
        assert "already exists" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_user_registration_invalid_email(self, async_client, db_session):
        """Test user registration with invalid email format."""
        # Arrange
        invalid_data = {
            "email": "not-an-email",
            "password": "ValidPassword123!",
            "name": "Test User",
            "preferred_language": "en"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/register",
            json=invalid_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
        assert any("email" in str(error).lower() for error in data["detail"])
    
    @pytest.mark.asyncio
    async def test_user_registration_weak_password(self, async_client, db_session):
        """Test user registration with weak password."""
        # Arrange
        weak_password_data = {
            "email": "<EMAIL>",
            "password": "weak",
            "name": "Test User",
            "preferred_language": "en"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/register",
            json=weak_password_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
        # Password should have validation rules
    
    @pytest.mark.asyncio
    async def test_user_login_success(self, async_client, db_session, test_user):
        """Test successful user login."""
        # Arrange
        login_data = {
            "username": test_user["email"],
            "password": "correct_password"  # In real test, use actual password
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/login",
            data=login_data,  # OAuth2 uses form data
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
    
    @pytest.mark.asyncio
    async def test_user_login_invalid_credentials(self, async_client, db_session, test_user):
        """Test login with invalid credentials."""
        # Arrange
        login_data = {
            "username": test_user["email"],
            "password": "wrong_password"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "detail" in data
        assert "incorrect" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_user_login_nonexistent_user(self, async_client, db_session):
        """Test login with non-existent user."""
        # Arrange
        login_data = {
            "username": "<EMAIL>",
            "password": "anypassword"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "detail" in data


class TestJWTTokenValidation:
    """Test JWT token validation and authorization (TEST-BE-003)."""
    
    @pytest.mark.asyncio
    async def test_valid_token_access(self, async_client, auth_headers):
        """Test accessing protected endpoint with valid token."""
        # Act
        response = await async_client.get(
            "/api/v1/users/me",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "id" in data
        assert "email" in data
        assert "password" not in data  # Should not expose password
    
    @pytest.mark.asyncio
    async def test_expired_token_access(self, async_client, expired_auth_headers):
        """Test accessing protected endpoint with expired token."""
        # Act
        response = await async_client.get(
            "/api/v1/users/me",
            headers=expired_auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "detail" in data
        assert "expired" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_invalid_token_format(self, async_client):
        """Test accessing protected endpoint with invalid token format."""
        # Arrange
        invalid_headers = {"Authorization": "Bearer invalid.token.here"}
        
        # Act
        response = await async_client.get(
            "/api/v1/users/me",
            headers=invalid_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "detail" in data
    
    @pytest.mark.asyncio
    async def test_missing_token(self, async_client):
        """Test accessing protected endpoint without token."""
        # Act
        response = await async_client.get("/api/v1/users/me")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        data = response.json()
        assert "detail" in data
        assert "not authenticated" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_malformed_authorization_header(self, async_client):
        """Test with malformed authorization header."""
        # Arrange
        malformed_headers = {"Authorization": "NotBearer token"}
        
        # Act
        response = await async_client.get(
            "/api/v1/users/me",
            headers=malformed_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestPasswordReset:
    """Test password reset functionality (TEST-BE-004)."""
    
    @pytest.mark.asyncio
    async def test_password_reset_request(self, async_client, db_session, test_user, mock_sendgrid):
        """Test requesting password reset."""
        # Arrange
        reset_data = {"email": test_user["email"]}
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/password-reset/request",
            json=reset_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert "sent" in data["message"].lower()
        
        # Verify email was sent
        mock_sendgrid.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_password_reset_request_nonexistent_email(self, async_client, db_session, mock_sendgrid):
        """Test password reset for non-existent email."""
        # Arrange
        reset_data = {"email": "<EMAIL>"}
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/password-reset/request",
            json=reset_data
        )
        
        # Assert
        # Should still return 200 to prevent email enumeration
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        
        # But email should not be sent
        mock_sendgrid.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_password_reset_confirm(self, async_client, db_session, test_user):
        """Test confirming password reset with valid token."""
        # Arrange
        reset_token = jwt.encode(
            {
                "sub": test_user["id"],
                "type": "password_reset",
                "exp": datetime.utcnow() + timedelta(hours=1)
            },
            "test-secret-key",
            algorithm="HS256"
        )
        
        reset_data = {
            "token": reset_token,
            "new_password": "NewSecurePassword123!"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/password-reset/confirm",
            json=reset_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert "reset" in data["message"].lower()
    
    @pytest.mark.asyncio
    async def test_password_reset_expired_token(self, async_client, db_session, test_user):
        """Test password reset with expired token."""
        # Arrange
        expired_token = jwt.encode(
            {
                "sub": test_user["id"],
                "type": "password_reset",
                "exp": datetime.utcnow() - timedelta(hours=1)
            },
            "test-secret-key",
            algorithm="HS256"
        )
        
        reset_data = {
            "token": expired_token,
            "new_password": "NewSecurePassword123!"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/password-reset/confirm",
            json=reset_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "detail" in data
        assert "expired" in data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_password_reset_invalid_token(self, async_client, db_session):
        """Test password reset with invalid token."""
        # Arrange
        reset_data = {
            "token": "invalid.token.here",
            "new_password": "NewSecurePassword123!"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/password-reset/confirm",
            json=reset_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "detail" in data
        assert "invalid" in data["detail"].lower()


class TestRefreshToken:
    """Test refresh token functionality."""
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, async_client, db_session):
        """Test refreshing access token with valid refresh token."""
        # Arrange
        refresh_token = jwt.encode(
            {
                "sub": "test-user-id",
                "type": "refresh",
                "exp": datetime.utcnow() + timedelta(days=7)
            },
            "test-secret-key",
            algorithm="HS256"
        )
        
        # Act
        response = await async_client.post(
            "/api/v1/auth/refresh",
            json={"refresh_token": refresh_token}
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
    
    @pytest.mark.asyncio
    async def test_refresh_token_invalid(self, async_client):
        """Test refresh with invalid refresh token."""
        # Act
        response = await async_client.post(
            "/api/v1/auth/refresh",
            json={"refresh_token": "invalid.refresh.token"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestOAuth2Integration:
    """Test OAuth2 social login integration."""
    
    @pytest.mark.asyncio
    async def test_google_oauth_callback(self, async_client, db_session):
        """Test Google OAuth callback handling."""
        # Arrange
        with patch('app.services.oauth.verify_google_token') as mock_verify:
            mock_verify.return_value = {
                "email": "<EMAIL>",
                "name": "OAuth User",
                "sub": "google-user-id"
            }
            
            # Act
            response = await async_client.post(
                "/api/v1/auth/google/callback",
                json={"token": "google-oauth-token"}
            )
            
            # Assert
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "access_token" in data
            assert "user" in data
            assert data["user"]["email"] == "<EMAIL>"


class TestUserProfile:
    """Test user profile endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_user_profile(self, async_client, auth_headers, test_user):
        """Test getting current user profile."""
        # Act
        response = await async_client.get(
            "/api/v1/users/me",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["email"] == test_user["email"]
        assert data["name"] == test_user["name"]
        assert "password" not in data
    
    @pytest.mark.asyncio
    async def test_update_user_profile(self, async_client, auth_headers):
        """Test updating user profile."""
        # Arrange
        update_data = {
            "name": "Updated Name",
            "preferred_language": "es",
            "timezone": "America/New_York"
        }
        
        # Act
        response = await async_client.patch(
            "/api/v1/users/me",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["preferred_language"] == update_data["preferred_language"]
        assert data["timezone"] == update_data["timezone"]
    
    @pytest.mark.asyncio
    async def test_change_password(self, async_client, auth_headers):
        """Test changing user password."""
        # Arrange
        password_data = {
            "current_password": "CurrentPassword123!",
            "new_password": "NewSecurePassword123!"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/users/me/change-password",
            json=password_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "message" in data
        assert "changed" in data["message"].lower()
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, async_client, auth_headers):
        """Test changing password with wrong current password."""
        # Arrange
        password_data = {
            "current_password": "WrongPassword123!",
            "new_password": "NewSecurePassword123!"
        }
        
        # Act
        response = await async_client.post(
            "/api/v1/users/me/change-password",
            json=password_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "detail" in data
        assert "incorrect" in data["detail"].lower()


# Unit tests for authentication utilities
class TestAuthenticationUtilities:
    """Test authentication helper functions."""
    
    def test_password_hashing(self):
        """Test password hashing and verification."""
        from app.core.security import get_password_hash, verify_password
        
        # Arrange
        password = "TestPassword123!"
        
        # Act
        hashed = get_password_hash(password)
        
        # Assert
        assert hashed != password
        assert verify_password(password, hashed) is True
        assert verify_password("WrongPassword", hashed) is False
    
    def test_create_access_token(self):
        """Test JWT token creation."""
        from app.core.security import create_access_token
        
        # Arrange
        user_id = "test-user-123"
        
        # Act
        token = create_access_token(data={"sub": user_id})
        
        # Assert
        decoded = jwt.decode(token, "test-secret-key", algorithms=["HS256"])
        assert decoded["sub"] == user_id
        assert "exp" in decoded
    
    def test_create_access_token_with_expiry(self):
        """Test JWT token with custom expiry."""
        from app.core.security import create_access_token
        
        # Arrange
        user_id = "test-user-123"
        expires_delta = timedelta(minutes=15)
        
        # Act
        token = create_access_token(
            data={"sub": user_id},
            expires_delta=expires_delta
        )
        
        # Assert
        decoded = jwt.decode(token, "test-secret-key", algorithms=["HS256"])
        exp_time = datetime.fromtimestamp(decoded["exp"])
        expected_exp = datetime.utcnow() + expires_delta
        
        # Allow 1 second difference for test execution time
        assert abs((exp_time - expected_exp).total_seconds()) < 1
