"""
Global pytest configuration and fixtures for EduAI Global tests.
"""
import asyncio
import pytest
from typing import Generator, AsyncGenerator
from datetime import datetime, timedelta
import jwt
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient
import redis
from unittest.mock import Mock, patch
import os
from uuid import uuid4

# Import your app dependencies (adjust imports based on actual structure)
# from app.main import app
# from app.core.config import settings
# from app.core.database import Base, get_db
# from app.models import User, Course, Module, UserProgress
# from app.core.security import create_access_token, get_password_hash

# For now, I'll create mock versions
from dataclasses import dataclass
from pydantic import BaseSettings


class Settings(BaseSettings):
    """Test settings configuration"""
    database_url: str = "sqlite+aiosqlite:///:memory:"
    database_url_sync: str = "sqlite:///:memory:"
    redis_url: str = "redis://localhost:6379/1"
    secret_key: str = "test-secret-key-for-jwt-encoding"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    openai_api_key: str = "test-openai-key"
    stripe_secret_key: str = "test-stripe-key"
    sendgrid_api_key: str = "test-sendgrid-key"
    
    class Config:
        env_file = ".env.test"


# Test settings instance
test_settings = Settings()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create async test database engine."""
    from sqlalchemy.ext.asyncio import create_async_engine
    engine = create_async_engine(
        test_settings.database_url,
        echo=False,
        future=True
    )
    yield engine
    await engine.dispose()


@pytest.fixture(scope="session")
def test_engine_sync():
    """Create sync test database engine for certain operations."""
    from sqlalchemy import create_engine
    engine = create_engine(
        test_settings.database_url_sync,
        echo=False,
        future=True
    )
    yield engine
    engine.dispose()


@pytest.fixture(scope="function")
async def db_session(test_engine):
    """Create a test database session."""
    # Import your Base from models
    from sqlalchemy.ext.declarative import declarative_base
    Base = declarative_base()
    
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async_session_maker = async_sessionmaker(
        test_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    async with async_session_maker() as session:
        yield session
        await session.rollback()
    
    # Clean up tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture(scope="function")
def db_session_sync(test_engine_sync):
    """Create a sync test database session for certain tests."""
    from sqlalchemy.ext.declarative import declarative_base
    Base = declarative_base()
    
    # Create tables
    Base.metadata.create_all(bind=test_engine_sync)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine_sync)
    session = SessionLocal()
    
    yield session
    
    session.close()
    Base.metadata.drop_all(bind=test_engine_sync)


@pytest.fixture
def test_client():
    """Create a test client for the FastAPI app."""
    # Import your actual app here
    # from app.main import app
    
    # Mock app for testing
    from fastapi import FastAPI
    app = FastAPI()
    
    with TestClient(app) as client:
        yield client


@pytest.fixture
async def async_client():
    """Create an async test client for the FastAPI app."""
    # Import your actual app here
    # from app.main import app
    
    # Mock app for testing
    from fastapi import FastAPI
    app = FastAPI()
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def redis_client():
    """Create a Redis client for testing."""
    client = redis.Redis.from_url(test_settings.redis_url, decode_responses=True)
    yield client
    client.flushdb()
    client.close()


@pytest.fixture
def mock_redis():
    """Mock Redis client for unit tests."""
    with patch('redis.Redis') as mock:
        mock_instance = Mock()
        mock.from_url.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def auth_headers():
    """Generate authentication headers with a valid JWT token."""
    token_data = {
        "sub": "test-user-id",
        "email": "<EMAIL>",
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    token = jwt.encode(token_data, test_settings.secret_key, algorithm=test_settings.algorithm)
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers():
    """Generate admin authentication headers."""
    token_data = {
        "sub": "admin-user-id",
        "email": "<EMAIL>",
        "role": "admin",
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    token = jwt.encode(token_data, test_settings.secret_key, algorithm=test_settings.algorithm)
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def expired_auth_headers():
    """Generate expired authentication headers for testing."""
    token_data = {
        "sub": "test-user-id",
        "email": "<EMAIL>",
        "exp": datetime.utcnow() - timedelta(minutes=30)
    }
    token = jwt.encode(token_data, test_settings.secret_key, algorithm=test_settings.algorithm)
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "SecurePassword123!",
        "name": "Test User",
        "preferred_language": "en",
        "timezone": "UTC"
    }


@pytest.fixture
def sample_course_data():
    """Sample course data for testing."""
    return {
        "title": {"en": "Introduction to AI", "es": "Introducción a la IA"},
        "description": {"en": "Learn AI basics", "es": "Aprende los básicos de IA"},
        "difficulty_level": "beginner",
        "estimated_hours": 10,
        "prerequisites": [],
        "tags": ["ai", "machine-learning", "beginner"],
        "is_premium": False
    }


@pytest.fixture
def sample_module_data():
    """Sample module data for testing."""
    return {
        "title": {"en": "What is AI?", "es": "¿Qué es IA?"},
        "content_type": "video",
        "duration": 600,  # 10 minutes
        "content_url": "https://example.com/video1.mp4",
        "order_index": 1,
        "is_premium": False,
        "offline_available": True
    }


@pytest.fixture
def mock_openai():
    """Mock OpenAI API for testing."""
    with patch('openai.ChatCompletion.create') as mock:
        mock.return_value = {
            "choices": [{
                "message": {
                    "content": "Mocked AI response"
                }
            }]
        }
        yield mock


@pytest.fixture
def mock_stripe():
    """Mock Stripe API for testing."""
    with patch('stripe.Customer.create') as mock_customer, \
         patch('stripe.Subscription.create') as mock_subscription:
        
        mock_customer.return_value = {"id": "cus_test123"}
        mock_subscription.return_value = {
            "id": "sub_test123",
            "status": "active",
            "current_period_start": int(datetime.utcnow().timestamp()),
            "current_period_end": int((datetime.utcnow() + timedelta(days=30)).timestamp())
        }
        
        yield {
            "customer": mock_customer,
            "subscription": mock_subscription
        }


@pytest.fixture
def mock_sendgrid():
    """Mock SendGrid API for testing."""
    with patch('sendgrid.SendGridAPIClient.send') as mock:
        mock.return_value = Mock(status_code=202)
        yield mock


@pytest.fixture
async def test_user(db_session):
    """Create a test user in the database."""
    # This would use your actual User model
    # For now, returning mock data
    user_data = {
        "id": str(uuid4()),
        "email": "<EMAIL>",
        "name": "Test User",
        "preferred_language": "en",
        "subscription_tier": "free",
        "created_at": datetime.utcnow()
    }
    # In real implementation:
    # user = User(**user_data)
    # db_session.add(user)
    # await db_session.commit()
    # await db_session.refresh(user)
    return user_data


@pytest.fixture
async def test_course(db_session):
    """Create a test course in the database."""
    course_data = {
        "id": str(uuid4()),
        "title": {"en": "Test Course"},
        "description": {"en": "Test Description"},
        "difficulty_level": "beginner",
        "estimated_hours": 5,
        "is_premium": False,
        "created_at": datetime.utcnow()
    }
    # In real implementation:
    # course = Course(**course_data)
    # db_session.add(course)
    # await db_session.commit()
    # await db_session.refresh(course)
    return course_data


@pytest.fixture
def mock_celery():
    """Mock Celery tasks for testing."""
    with patch('celery.Task.apply_async') as mock:
        mock.return_value = Mock(id="task-id-123")
        yield mock


# Markers for different test categories
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
pytest.mark.security = pytest.mark.security
pytest.mark.performance = pytest.mark.performance
