# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
pip-log.txt
pip-delete-this-directory.txt

# Virtual Environments
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# Poetry
poetry.lock

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Ruff
.ruff_cache/

# Environments
.env
.env.local
.env.*.local
.venv

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/
.parcel-cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
*.sqlite3
*.db
*.db-journal
*.db-wal

# Redis
dump.rdb

# Celery
celerybeat-schedule
celerybeat.pid

# Project specific
/backend/alembic.ini
/backend/app/uploaded_files/
/frontend/.eslintcache
/frontend/playwright-report/
/frontend/test-results/
/coverage/
/reports/

# Local environment overrides
docker-compose.override.yml
.env.development.local
.env.test.local
.env.production.local
