---
description: 
globs: 
alwaysApply: false
---
# Monorepo Best Practices

## 1. Project Structure
- Organize code by domain or feature, not just by type (e.g., `frontend/`, `server/`, `project/`).
- Use clear naming conventions for directories and files.
- Keep test, config, and documentation files close to the code they relate to.

## 2. Linting & Formatting
- **Frontend (TypeScript/React):**
  - Use ESLint with TypeScript and React plugins. See [frontend/.eslintrc.cjs](mdc:frontend/.eslintrc.cjs) and [frontend/eslint.config.js](mdc:frontend/eslint.config.js).
  - Use Prettier for consistent code formatting. See [frontend/.prettierrc](mdc:frontend/.prettierrc).
  - Add lint and format scripts in [frontend/package.json](mdc:frontend/package.json):
    ```json
    "lint": "eslint . --ext .ts,.tsx",
    "format": "prettier --write ."
    ```
  - Run linting and formatting in CI before merging PRs.

- **Backend (Python):**
  - Use Ruff for linting and Black for formatting.
  - Use MyPy for static type checking.
  - Example scripts in `pyproject.toml` or `Makefile`:
    ```sh
    ruff check .
    black .
    mypy .
    ```
  - Enforce lint/type checks in CI.

## 3. Testing
- **Frontend:**
  - Use Vitest for unit tests, Testing Library for React component tests, and Playwright for E2E tests.
  - Place tests alongside components or in a `__tests__` directory.
  - Example scripts in [frontend/package.json](mdc:frontend/package.json):
    ```json
    "test": "vitest",
    "test:e2e": "playwright test"
    ```
  - Require tests for new features and bug fixes.

- **Backend:**
  - Use Pytest for all Python tests.
  - Organize tests into `unit/`, `integration/`, and `e2e/` subfolders. See [project/test_proj1/backend/tests/](mdc:project/test_proj1/backend/tests).
  - Example command: `pytest`
  - Require tests for all new backend logic.

## 4. Code Quality
- Use strong typing (TypeScript for frontend, type hints for Python).
- Keep files under 500 lines for maintainability.
- Refactor regularly to reduce complexity.
- Use code reviews and automated CI checks for all merges.

## 5. CI/CD
- Run lint, format, and test scripts for both frontend and backend in CI.
- Block merges on failed lint, format, or test checks.
- Generate and review code coverage reports.

## 6. Security & Privacy
- Never commit secrets or API keys.
- Use environment variables for sensitive data.
- Review AI privacy settings in Cursor.

## 7. Documentation
- Maintain up-to-date `README.md` files in each major directory.
- Document setup, scripts, and development workflow.
- Reference key files using `[filename.ext](mdc:filename.ext)`.
