# EduAI Global Documentation

## Overview
EduAI Global is a revolutionary AI education platform designed to democratize access to AI literacy worldwide. By providing adaptive learning paths, practical projects, and global community support, we're empowering millions to participate in the AI-driven future.

## Quick Links
- [Vision](./vision.md)
- [Architecture](./architecture/system-overview.md)
- [Tech Stack](./architecture/tech-stack.md)
- [Current State](./state/current-state.md)
- [Roadmap](./roadmap/milestones.md)

## Getting Started

### Prerequisites
- Node.js 18+ (Frontend)
- Python 3.11+ (Backend)
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### Quick Setup
```bash
# Clone the repository
git clone https://github.com/eduai-global/platform.git
cd platform

# Install dependencies
make install

# Start development environment
make dev

# Run tests
make test
```

## Project Structure
```
eduai-global/
├── docs/                  # Documentation
├── slides/               # RevealJS presentations
├── tests/                # Test suites
├── backend/              # FastAPI application
│   ├── app/
│   │   ├── api/         # API endpoints
│   │   ├── core/        # Core utilities
│   │   ├── models/      # Database models
│   │   ├── schemas/     # Pydantic schemas
│   │   └── services/    # Business logic
│   └── migrations/      # Database migrations
├── frontend/            # Next.js application
│   ├── components/      # React components
│   ├── pages/          # Next.js pages
│   ├── services/       # API clients
│   └── types/          # TypeScript types
└── infrastructure/     # IaC and deployment

```

## Contributing

### Development Workflow
1. **Documentation First**: Update relevant documentation
2. **Test Driven**: Write tests before implementation
3. **Type Safety**: Use TypeScript/Pydantic for all interfaces
4. **Code Review**: All PRs require review

### Code Standards
- Backend: Follow PEP 8, use Black formatter
- Frontend: ESLint + Prettier configuration
- Commits: Conventional commits format
- Testing: Minimum 80% coverage

### Pull Request Process
1. Create feature branch from `develop`
2. Update documentation if needed
3. Write/update tests
4. Implement changes
5. Ensure all tests pass
6. Submit PR with detailed description
7. Address review feedback

## Architecture Overview
EduAI Global follows a microservices architecture with:
- **Frontend**: Next.js with TypeScript
- **Backend**: FastAPI with async PostgreSQL
- **Cache**: Redis for session and content caching
- **Queue**: Celery for background tasks
- **ML Services**: Separate containers for AI models
- **CDN**: CloudFront for global content delivery

## Support
- Documentation: [docs.eduai.global](https://docs.eduai.global)
- Issues: [GitHub Issues](https://github.com/eduai-global/platform/issues)
- Community: [Discord Server](https://discord.gg/eduai)
- Email: <EMAIL>
