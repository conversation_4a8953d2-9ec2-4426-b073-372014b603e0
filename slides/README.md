# BrahmaSumm Presentation Slides

This folder contains the presentation slides for BrahmaSumm, built using [reveal.js](https://revealjs.com/).

## Structure

- `index.html` - The main presentation file
- `css/custom.css` - Custom styling for the presentation
- `js/custom.js` - Custom JavaScript functionality
- `slide_plan.md` - The content outline for the presentation

## How to View

Simply open `index.html` in a web browser to view the presentation.

## Keyboard Shortcuts

- Press `F` to go fullscreen
- Press `ESC` to exit fullscreen
- Press `Alt + D` to toggle between light and dark mode
- Press `S` to open speaker notes
- Use arrow keys or spacebar to navigate slides
- Press `?` to see all available keyboard shortcuts

## Features

- Responsive design that works on different screen sizes
- Dark/light mode toggle
- Speaker notes support
- Interactive elements and animations
- Progress bar
- Slide numbers
- Print support (use browser print function)

## Customization

To customize the presentation:

1. Edit `index.html` to modify slide content
2. Edit `css/custom.css` to change styling
3. Edit `js/custom.js` to add or modify interactive features