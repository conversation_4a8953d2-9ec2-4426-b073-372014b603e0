{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "full path to your windows / mac code"
      ]
    }
  }
}

Example: 
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Code/KAPI/",
        "/Users/<USER>/Code/kapi-fresh/",
        "/Users/<USER>/Code/Kapi_biz/templates/",
        "/Users/<USER>/Code/experimental"
      ]
    }
  }
}